/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.kylinRestful.http;

import com.dtstack.dtcenter.loader.dto.source.KylinRestfulSourceDTO;

public class HttpAddressManager {

    private final String address;

    public static final String HTTP_PREFIX = "http://";

    private static final String HTTPS_PREFIX = "https://";

    private HttpAddressManager(KylinRestfulSourceDTO sourceDTO) {
        // 默认 http 协议
        address = (sourceDTO.getUrl().startsWith(HTTP_PREFIX) || sourceDTO.getUrl().startsWith(HTTPS_PREFIX)) ?
                sourceDTO.getUrl() : HTTP_PREFIX + sourceDTO.getUrl();
    }

    public static HttpAddressManager createHttpAddressManager(KylinRestfulSourceDTO sourceDTO) {
        return new HttpAddressManager(sourceDTO);
    }

    public String getAddress() {
        return this.address;
    }
}
