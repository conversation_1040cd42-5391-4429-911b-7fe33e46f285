package com.dtstack.dtcenter.loader.client.sql;

import com.dtstack.dtcenter.loader.cache.pool.config.PoolConfig;
import com.dtstack.dtcenter.loader.client.ClientCache;
import com.dtstack.dtcenter.loader.client.IClient;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.MariaDBSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.Mysql5SourceDTO;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-10-17-14:47
 * @description:
 */
public class MariadbTest {

    // 获取数据源 client
    private static final IClient client = ClientCache.getClient(DataSourceType.MariaDB.getVal());
    // 构建数据源信息
    private static final MariaDBSourceDTO source = MariaDBSourceDTO.builder()
            .url("jdbc:mysql://*************:32768/sh_test")
            .username("root")
            .password("Cdyanfa_123456")
            .poolConfig(PoolConfig.builder().build())
            .build();


    /**
     * 根据schema获取表
     */
    @Test
    public void getTableListBySchema_001() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().schema("sh_test").build();
        List<String> tableList = client.getTableListBySchema(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(tableList));
    }

    /**
     * 选择schema
     */
    @Test
    public void getTableList_0001() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().build();
        List<String> tableList = client.getTableList(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(tableList));
        for (String s : tableList) {
            System.out.println(s);
        }
    }

}
