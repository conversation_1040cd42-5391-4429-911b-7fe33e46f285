/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.client.common;

import com.dtstack.dtcenter.common.loader.common.utils.CollectionUtil;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;

/**
 * 集合工具类测试
 *
 * <AUTHOR>
 * date：Created in 上午11:31 2021/6/4
 * company: www.dtstack.com
 */
public class CollectionUtilTest {

    /**
     * 集合转 string
     */
    @Test
    public void listToStr() {
        String toStr = CollectionUtil.listToStr(Lists.newArrayList("a", "b", "", "d"));
        Assert.assertEquals(toStr, "a,b,d");
    }
}
