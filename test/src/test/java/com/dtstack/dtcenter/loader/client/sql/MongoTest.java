/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.client.sql;

import com.dsg.database.datasource.dto.DatasourceDTO;
import com.dsg.database.datasource.utils.DatasourceUtils;
import com.dtstack.dtcenter.loader.cache.pool.config.PoolConfig;
import com.dtstack.dtcenter.loader.client.BaseTest;
import com.dtstack.dtcenter.loader.client.ClientCache;
import com.dtstack.dtcenter.loader.client.IClient;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.MongoSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;
import java.util.Map;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 14:07 2020/2/29
 * @Description：Mongo 测试
 */
public class MongoTest extends BaseTest {

    // 构建client
    private static final IClient client = ClientCache.getClient(DataSourceType.MONGODB.getVal());

    // 构建数据源信息
    private static final MongoSourceDTO source = MongoSourceDTO.builder()
            .hostPort("**************:27017/admin")
//            .username("mongo")
//            .password("Cdyanfa_123456")
//            .schema("test")
            .poolConfig(new PoolConfig())
            .build();

    /**
     * 连通性测试
     */
    @Test
    public void testCon() {
        Boolean isConnected = client.testCon(source);
        if (Boolean.FALSE.equals(isConnected)) {
            throw new DtLoaderException("connection exception");
        }
    }

    @Test
    public void getTableList() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().build();
        List<String> tableList = client.getTableList(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(tableList));
    }

    @Test
    public void getDatabaseList() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().build();
        List list = client.getAllDatabases(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    }

    @Test
    public void getPreview() {
        IClient client = ClientCache.getClient(DataSourceType.MONGODB.getVal());
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().tableName("system.version").build();
        List<List<Object>> preview = client.getPreview(source, queryDTO);
        preview.forEach(list->{
            list.forEach(pair->{
                Pair p = (Pair)pair;
                System.out.println(p.getKey()+"   "+p.getValue());
            });
        });

    }

    @Test
    public void executorQuery() {
        IClient<List> client = ClientCache.getClient(DataSourceType.MONGODB.getVal());
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql("db.user.find({});").startRow(1).limit(5).build();
        List<Map<String, Object>> result = client.executeQuery(source, queryDTO);
        result.forEach(map->{
            map.keySet().forEach(x->{
                System.out.println(x+"==="+map.get(x));
            });
        });
    }

    /**
     * aggregate
     */
    @Test
    public void executoraggregate() {
        IClient<List> client = ClientCache.getClient(DataSourceType.MONGODB.getVal());
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql("db.user.aggregate([{$group : {_id : \"$by_user\", num_tutorial : {$sum : 1}}}])").build();
        List<Map<String, Object>> result = client.executeQuery(source, queryDTO);
        result.forEach(map->{
            map.keySet().forEach(x->{
                System.out.println(x+"==="+map.get(x));
            });
        });
    }

    /**
     * count
     */
    @Test
    public void executorCount() {
        IClient<List> client = ClientCache.getClient(DataSourceType.MONGODB.getVal());
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql(" db.user.count()").build();
        List<Map<String, Object>> result = client.executeQuery(source, queryDTO);
        result.forEach(map->{
            map.keySet().forEach(x->{
                System.out.println(x+"==="+map.get(x));
            });
        });
    }

    /**
     * findOne
     */
    @Test
    public void executorFindOne() {
        IClient<List> client = ClientCache.getClient(DataSourceType.MONGODB.getVal());
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql("db.user.findOne({ })").build();
        List<Map<String, Object>> result = client.executeQuery(source, queryDTO);
        result.forEach(map->{
            map.keySet().forEach(x->{
                System.out.println(x+"==="+map.get(x));
            });
        });
    }


    /**
     * findOne
     */
    @Test
    public void executorDistinct() {
        IClient<List> client = ClientCache.getClient(DataSourceType.MONGODB.getVal());
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql(" db.inventory.distinct( \"dept\" )").build();
        List<Map<String, Object>> result = client.executeQuery(source, queryDTO);
        result.forEach(map->{
            map.keySet().forEach(x->{
                System.out.println(x+"==="+map.get(x));
            });
        });
    }

    /**
     * 获取表字段详细信息
     */
    @Test
    public void getColumnMetaData()  {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().tableName("student0414").build();
        List<ColumnMetaDTO> columnMetaData = client.getColumnMetaData(source, queryDTO);
        Assert.assertEquals("BIGINT", columnMetaData.get(0).getType());
        Assert.assertEquals("VARCHAR", columnMetaData.get(1).getType());
        Assert.assertTrue(CollectionUtils.isNotEmpty(columnMetaData));
    }

    /**
     * 获取正在使用的database
     */
    @Test
    public void getCurrentDatabase() {
        DatasourceDTO datasourceDTO = DatasourceDTO.builder()
                .dataJson("********************************************************************************************************************************************************************************************************************************************************************************************************************************")
//                .dataType("AWS S3")
//                .dataVersion("3.12.4")
                .dataTypeCode(13)
                .build();
        String currentDatabase = DatasourceUtils.getCurrentDatabase(datasourceDTO);

//        String currentDatabase = client.getCurrentDatabase(source);
        System.out.println(currentDatabase);

    }

}
