/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.client.sql;

import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.dto.DatasourceDTO;
import com.dsg.database.datasource.utils.DatasourceUtils;
import com.dtstack.dtcenter.common.loader.hadoop.util.KerberosLoginUtil;
import com.dtstack.dtcenter.common.loader.hbase.util.DsgExportSnapshot;
//import com.dtstack.dtcenter.common.loader.kafka.util.KafkaUtil;
import com.dtstack.dtcenter.loader.cache.pool.config.PoolConfig;
import com.dtstack.dtcenter.loader.client.BaseTest;
import com.dtstack.dtcenter.loader.client.ClientCache;
import com.dtstack.dtcenter.loader.client.IHbase;
import com.dtstack.dtcenter.loader.client.IKerberos;
import com.dtstack.dtcenter.loader.dto.HbaseQueryDTO;
import com.dtstack.dtcenter.loader.dto.comparator.BinaryComparator;
import com.dtstack.dtcenter.loader.dto.filter.FilterList;
import com.dtstack.dtcenter.loader.dto.filter.RowFilter;
import com.dtstack.dtcenter.loader.dto.filter.SingleColumnValueFilter;
import com.dtstack.dtcenter.loader.dto.source.HbaseSourceDTO;
import com.dtstack.dtcenter.loader.enums.CompareOp;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.kerberos.HadoopConfTool;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.security.UserGroupInformation;
import org.junit.Assert;
import org.junit.Test;

import java.io.File;
import java.io.FileInputStream;
import java.util.*;

/**
 * hbase新客户端无kerberos认证测试
 *
 * <AUTHOR>
 * date：Created in 9:38 上午 2020/12/2
 * company: www.dtstack.com
 */
public class HbaseClientTest extends BaseTest {

    // 构建hbase client
    private static final IHbase HBASE_CLIENT = ClientCache.getHbase(DataSourceType.HBASE.getVal());

    // 连接池信息
    private static final PoolConfig poolConfig = PoolConfig.builder().maximumPoolSize(100).build();

    // 构建数据源信息
    private static final HbaseSourceDTO source = HbaseSourceDTO.builder()
            .url("192.168.2.7:2181,192.168.2.9:2181,192.168.2.10:2181")
            .path("/hbase-unsecure")
            .poolConfig(poolConfig)
            .build();

    // 构建数据源信息
    private static final HbaseSourceDTO source2 = HbaseSourceDTO.builder()
            .url("192.168.2.2:2181,192.168.2.3:2181,192.168.2.4:2181")
            .path("/hbase-secure")
            .poolConfig(poolConfig)
            .build();

    /**
     * 数据准备
     *//*
    @BeforeClass
    public static void setUp () {
        try {
            HBASE_CLIENT.createHbaseTable(source, "loader_test_2", new String[]{"info1", "info2"});
        } catch (Exception e) {
            // 目前插件化里没有方法支持判断表是否存在，异常不作处理
        }
        HBASE_CLIENT.putRow(source, "loader_test_2", "1001", "info1", "name", "wangchuan");
        HBASE_CLIENT.putRow(source, "loader_test_2", "1002", "info1", "name", "wangbin");
        HBASE_CLIENT.putRow(source, "loader_test_2", "1003", "info2", "name", "wangchuan");
        HBASE_CLIENT.putRow(source, "loader_test_2", "1003", "info2", "age", "18");
        HBASE_CLIENT.putRow(source, "loader_test_2", "1004", "info2", "name", "wangchuan4");
        HBASE_CLIENT.putRow(source, "loader_test_2", "1005", "info2", "name", "wangchuan5");
        HBASE_CLIENT.putRow(source, "loader_test_2", "1006", "info2", "name", "wangchuan6");
        HBASE_CLIENT.putRow(source, "loader_test_2", "1007", "info2", "name", "wangchuan7");
        HBASE_CLIENT.putRow(source, "loader_test_2", "1008", "info2", "name", "wangchuan8");
        HBASE_CLIENT.putRow(source, "loader_test_2", "1004_loader", "info2", "addr", "beijing");
        HBASE_CLIENT.putRow(source, "loader_test_2", "1005_loader", "info2", "addr", "shanghai");
        HBASE_CLIENT.putRow(source, "loader_test_2", "1006_loader", "info2", "addr", "shenzhen");
        HBASE_CLIENT.putRow(source, "loader_test_2", "1007_loader", "info2", "addr", "hangzhou");
    }*/

    /**
     * 自定义查询
     */
    @Test
    public void executeQuery() {
        // 最外层为 or
        FilterList filterList = new FilterList(FilterList.Operator.MUST_PASS_ONE);
        // rowKey > 1005
        RowFilter rowGreaterFilter = new RowFilter(CompareOp.GREATER, new BinaryComparator("1005".getBytes()));
        filterList.addFilter(rowGreaterFilter);
        // 这一层为 and
        FilterList filterList2 = new FilterList(FilterList.Operator.MUST_PASS_ALL);
        // rowKey < 1005
        RowFilter rowLessFilter = new RowFilter(CompareOp.LESS, new BinaryComparator("1005".getBytes()));
        // info2:name = wangchuan4
        SingleColumnValueFilter singleColumnValueFilter = new SingleColumnValueFilter("info2".getBytes(), "name".getBytes(), CompareOp.EQUAL, "wangchuan4".getBytes());
        // 设置为 true 标识当该列不存在不进行返回
        singleColumnValueFilter.setFilterIfMissing(true);
        filterList2.addFilter(rowLessFilter);
        filterList2.addFilter(singleColumnValueFilter);
        filterList.addFilter(filterList2);
        // 最后的查询条件相当于 select * from loader_test_2 where rowKey > 1002 and (rowKey > 1005 or (rowKey < 1005 and info2:name = wangchuan4)) limit 50
        HbaseQueryDTO hbaseQueryDTO = HbaseQueryDTO.builder()
                .tableName("loader_test_2")
                .startRowKey("1002")
                .filter(filterList)
                .limit(50L)
                .build();
        List<Map<String, Object>> queryResult = HBASE_CLIENT.executeQuery(source, hbaseQueryDTO, null);
        System.out.println(queryResult);
    }

    /**
     * 测试已经存在的namespace
     */
    @Test
    public void dbExists() {
        IHbase hbaseClient = ClientCache.getHbase(DataSourceType.HBASE.getVal());
        Boolean check = hbaseClient.isDbExists(source, "default");
        Assert.assertTrue(check);
    }

    /**
     * 测试不存在的namespace
     */
    @Test
    public void dbNotExists() {
        Boolean check = HBASE_CLIENT.isDbExists(source, UUID.randomUUID().toString());
        Assert.assertFalse(check);
    }

    /**
     * 创建已经存在的表测试
     */
    @Test(expected = DtLoaderException.class)
    public void createHbaseTableExists() {
        Boolean check = HBASE_CLIENT.createHbaseTable(source, "loader_test_2", new String[]{"info1", "info2"});
        Assert.assertFalse(check);
    }

    @Test
    public void deleteHbaseTable() {
        Boolean check = HBASE_CLIENT.deleteHbaseTable(source, "loader_test_2");
        Assert.assertTrue(check);
        Boolean check1 = HBASE_CLIENT.createHbaseTable(source, "loader_test_2", new String[]{"info1", "info2"});
        Assert.assertTrue(check1);
    }

    @Test
    public void deleteHbaseTable_1() {
        Boolean check = HBASE_CLIENT.deleteHbaseTable(source, "default", "loader_test_2");
        Assert.assertTrue(check);
        Boolean check1 = HBASE_CLIENT.createHbaseTable(source, "default", "loader_test_2", new String[]{"info1", "info2"});
        Assert.assertTrue(check1);
    }

    /**
     * 已注try-catch
     * 创建已经存在的表测试，需要测试自己手动修改表名，目前暂时不支持hbase删除表
     */
    @Test
    public void createHbaseTableNotExists() {
        try {
            Boolean check = HBASE_CLIENT.createHbaseTable(source, "_tableName", new String[]{"info1", "info2"});
            System.out.println(check);
        } catch (Exception e) {
            // 不作处理
        }
    }

    /**
     * 根据rowKey正则获取对应的rowKey列表
     */
    @Test
    public void scanByRegex() {
        List<String> list = HBASE_CLIENT.scanByRegex(source, "loader_test_2", ".*_loader");
        Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    }

    /**
     * 插入指定rowKey、列族、列名的数据
     */
    @Test
    public void putRow() {
        Boolean check = HBASE_CLIENT.putRow(source, "loader_test_2", "1002", "info1", "name", "wangchuan");
        Assert.assertTrue(check);
    }

    /**
     * 获取指定rowKey、列族、列名的数据
     */
    @Test
    public void getRow() {
        String row = HBASE_CLIENT.getRow(source, "loader_test_2", "1003", "info2", "name");
        Assert.assertTrue(org.apache.commons.lang3.StringUtils.isNotBlank(row));
    }

    /**
     * 删除指定rowKey、列族、列名的数据
     */
    @Test
    public void deleteByRowKey() {
        Boolean check = HBASE_CLIENT.deleteByRowKey(source, "loader_test_2", "info1", "name", Lists.newArrayList("1001", "1002"));
        Assert.assertTrue(check);
    }

    /**
     * 数据预览
     */
    @Test
    public void preview() {
        List<List<String>> preview = HBASE_CLIENT.preview(source, "loader_test_2", 10);
        Assert.assertTrue(CollectionUtils.isNotEmpty(preview));
    }

    /**
     * 数据预览
     */
    @Test
    public void preview2() {
        List<List<String>> preview = HBASE_CLIENT.preview(source, "loader_test_2", Lists.newArrayList("info2"), 1);
        Assert.assertTrue(CollectionUtils.isNotEmpty(preview));
    }

    /**
     * 数据预览
     */
    @Test
    public void preview3() {
        Map<String, List<String>> familyQualifierMap = Maps.newHashMap();
        List<String> info1Columns = Lists.newArrayList("name", "age");
        List<String> info2Columns = Lists.newArrayList("addr", "name", "age");
        familyQualifierMap.put("info1", info1Columns);
        familyQualifierMap.put("info2", info2Columns);
        List<List<String>> preview = HBASE_CLIENT.preview(source, "loader_test_2", familyQualifierMap, 10);
        Assert.assertTrue(CollectionUtils.isNotEmpty(preview));
    }

    /**
     * 数据预览 无数据测试
     */
    @Test
    public void preview4() {
        Map<String, List<String>> familyQualifierMap = Maps.newHashMap();
        List<String> info1Columns = Lists.newArrayList("xxx");
        familyQualifierMap.put("info1", info1Columns);
        List<List<String>> preview = HBASE_CLIENT.preview(source, "loader_test_2", familyQualifierMap, 10);
        Assert.assertEquals(0, preview.size());
    }

    @Test
    public void getALLNamespace() {
        List<Object> allTableDescriptor = HBASE_CLIENT.getAllTableDescriptor(source);
        Assert.assertTrue(CollectionUtils.isNotEmpty(allTableDescriptor));
    }

    public List<JSONObject> getAllNameSpaces() throws Exception {
        Map<String, Object> kerberosConfig = new HashMap<>();
        kerberosConfig.put(HadoopConfTool.PRINCIPAL_FILE, "hbase.service.keytab");
        kerberosConfig.put(HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF, "krb5.conf");
        kerberosConfig.put("hbase.regionserver.kerberos.principal", "hbase/<EMAIL>");
        kerberosConfig.put("hbase.master.kerberos.principal", "hbase/<EMAIL>");
        String localKerberosPath = "E:\\\\hadoop_resource\\\\hdp2.2\\";
        kerberosConfig.put("keytabPath", "E:\\\\hadoop_resource\\\\hdp2.2\\\\hbase.service.keytab");
        IKerberos kerberos = ClientCache.getKerberos(DataSourceType.HBASE.getVal());
        kerberos.prepareKerberosForConnect(kerberosConfig, localKerberosPath);


        List<File> files = new ArrayList<>();
        File corefile = new File("E:\\hadoop_resource\\hdp2.2\\core-site.xml");
        File hdfsfile = new File("E:\\hadoop_resource\\hdp2.2\\hdfs-site.xml");
//        File yarnfile = new File("E:\\hadoop_resource\\hdp2.7\\yarn-site.xml");
//        File mapfile = new File("E:\\hadoop_resource\\hdp2.7\\mapred-site.xml");
        File hbasefile = new File("E:\\hadoop_resource\\hdp2.2\\hbase-site.xml");
        files.add(corefile);
        files.add(hdfsfile);
//        files.add(yarnfile);
//        files.add(mapfile);
        files.add(hbasefile);
        Map<String, String> confMap = HdfsTest.getconf(files);
        DatasourceDTO datasourceDTO = new DatasourceDTO();
        datasourceDTO.setDataTypeCode(DataSourceType.HBASE.getVal());
        datasourceDTO.setDataType("HBase");
        datasourceDTO.setDataVersion("1.x");
        JSONObject jsonObject1 = new JSONObject();
        String s1 = JSONObject.toJSONString(confMap);
        jsonObject1.put("hbaseConfig", s1);
        datasourceDTO.setDataJsonMap(jsonObject1);
        kerberosConfig.putAll(confMap);
        List<JSONObject> nameSpaces = DatasourceUtils.getAllNameSpaces(datasourceDTO, kerberosConfig, "");
        System.out.println(nameSpaces);
        return nameSpaces;

    }


    public List<Object> getAllTables() throws Exception {
        Map<String, Object> kerberosConfig = new HashMap<>();
        kerberosConfig.put(HadoopConfTool.PRINCIPAL_FILE, "hbase.service.keytab");
        kerberosConfig.put(HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF, "krb5.conf");
        kerberosConfig.put("hbase.regionserver.kerberos.principal", "hbase/<EMAIL>");
        kerberosConfig.put("hbase.master.kerberos.principal", "hbase/<EMAIL>");
        String localKerberosPath = "E:\\\\hadoop_resource\\\\hdp2.2\\";
        kerberosConfig.put("keytabPath", "E:\\\\hadoop_resource\\\\hdp2.2\\\\hbase.service.keytab");
        IKerberos kerberos = ClientCache.getKerberos(DataSourceType.HBASE.getVal());
        kerberos.prepareKerberosForConnect(kerberosConfig, localKerberosPath);


        List<File> files = new ArrayList<>();
        File corefile = new File("E:\\hadoop_resource\\hdp2.2\\core-site.xml");
        File hdfsfile = new File("E:\\hadoop_resource\\hdp2.2\\hdfs-site.xml");
//        File yarnfile = new File("E:\\hadoop_resource\\hdp2.7\\yarn-site.xml");
//        File mapfile = new File("E:\\hadoop_resource\\hdp2.7\\mapred-site.xml");
        File hbasefile = new File("E:\\hadoop_resource\\hdp2.2\\hbase-site.xml");
        files.add(corefile);
        files.add(hdfsfile);
//        files.add(yarnfile);
//        files.add(mapfile);
        files.add(hbasefile);
        Map<String, String> confMap = HdfsTest.getconf(files);
        DatasourceDTO datasourceDTO = new DatasourceDTO();
        datasourceDTO.setDataTypeCode(DataSourceType.HBASE.getVal());
        datasourceDTO.setDataType("HBase");
        datasourceDTO.setDataVersion("1.x");
        JSONObject jsonObject1 = new JSONObject();
        String s1 = JSONObject.toJSONString(confMap);
        jsonObject1.put("hbaseConfig", s1);
        datasourceDTO.setDataJsonMap(jsonObject1);
        kerberosConfig.putAll(confMap);
        List<Object> allTableDescriptor = DatasourceUtils.getAllTableDescriptor(datasourceDTO, kerberosConfig, "");
        System.out.println(allTableDescriptor);
        return allTableDescriptor;

    }

    @Test
    public void CreateTableSnap() throws Exception {
        Map<String, Object> kerberosConfig = new HashMap<>();
        kerberosConfig.put(HadoopConfTool.PRINCIPAL_FILE, "hbase.service.keytab");
        kerberosConfig.put(HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF, "krb5.conf");
        kerberosConfig.put("hbase.regionserver.kerberos.principal", "hbase/<EMAIL>");
        kerberosConfig.put("hbase.master.kerberos.principal", "hbase/<EMAIL>");
        String localKerberosPath = "E:\\\\hadoop_resource\\\\hdp2.2\\";
        kerberosConfig.put("keytabPath", "E:\\\\hadoop_resource\\\\hdp2.2\\\\hbase.service.keytab");
        IKerberos kerberos = ClientCache.getKerberos(DataSourceType.HBASE.getVal());
        kerberos.prepareKerberosForConnect(kerberosConfig, localKerberosPath);


        List<File> files = new ArrayList<>();
        File corefile = new File("E:\\hadoop_resource\\hdp2.2\\core-site.xml");
        File hdfsfile = new File("E:\\hadoop_resource\\hdp2.2\\hdfs-site.xml");
//        File yarnfile = new File("E:\\hadoop_resource\\hdp2.7\\yarn-site.xml");
//        File mapfile = new File("E:\\hadoop_resource\\hdp2.7\\mapred-site.xml");
        File hbasefile = new File("E:\\hadoop_resource\\hdp2.2\\hbase-site.xml");
        files.add(corefile);
        files.add(hdfsfile);
//        files.add(yarnfile);
//        files.add(mapfile);
        files.add(hbasefile);
        Map<String, String> confMap = HdfsTest.getconf(files);
        DatasourceDTO datasourceDTO = new DatasourceDTO();
        datasourceDTO.setDataTypeCode(DataSourceType.HBASE.getVal());
        datasourceDTO.setDataType("HBase");
        datasourceDTO.setDataVersion("1.x");
        JSONObject jsonObject1 = new JSONObject();
        String s1 = JSONObject.toJSONString(confMap);
        jsonObject1.put("hbaseConfig", s1);
        datasourceDTO.setDataJsonMap(jsonObject1);
        kerberosConfig.putAll(confMap);
        Boolean tableSnapshot = DatasourceUtils.createTableSnapshot(datasourceDTO, kerberosConfig, "mytable", "mytable_lf_601", "");
        System.out.println(tableSnapshot);
    }

    @Test
    public void exportSnap() throws Exception {
        Map<String, Object> kerberosConfig = new HashMap<>();
        kerberosConfig.put(HadoopConfTool.PRINCIPAL_FILE, "hbase.service.keytab");
        kerberosConfig.put(HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF, "krb5.conf");
        kerberosConfig.put("hbase.regionserver.kerberos.principal", "hbase/<EMAIL>");
        kerberosConfig.put("hbase.master.kerberos.principal", "hbase/<EMAIL>");
        String localKerberosPath = "E:\\\\hadoop_resource\\\\hdp2.2\\";
        kerberosConfig.put("keytabPath", "E:\\\\hadoop_resource\\\\hdp2.2\\\\hbase.service.keytab");
        IKerberos kerberos = ClientCache.getKerberos(DataSourceType.HBASE.getVal());
        kerberos.prepareKerberosForConnect(kerberosConfig, localKerberosPath);
        List<File> files = new ArrayList<>();
        File corefile = new File("E:\\hadoop_resource\\hdp2.2\\core-site.xml");
        File hdfsfile = new File("E:\\hadoop_resource\\hdp2.2\\hdfs-site.xml");
        File yarnfile = new File("E:\\hadoop_resource\\hdp2.2\\yarn-site.xml");
        File mapfile = new File("E:\\hadoop_resource\\hdp2.2\\mapred-site.xml");
        File hbasefile = new File("E:\\hadoop_resource\\hdp2.2\\hbase-site.xml");
        files.add(corefile);
        files.add(hdfsfile);
        files.add(yarnfile);
        files.add(mapfile);
        files.add(hbasefile);
        Map<String, String> confMap = HdfsTest.getconf(files);
        DatasourceDTO datasourceDTO = new DatasourceDTO();
        datasourceDTO.setDataTypeCode(DataSourceType.HBASE.getVal());
        datasourceDTO.setDataType("HBase");
        datasourceDTO.setDataVersion("1.x");
        JSONObject jsonObject1 = new JSONObject();
        String s1 = JSONObject.toJSONString(confMap);
        jsonObject1.put("hbaseConfig", s1);
        datasourceDTO.setDataJsonMap(jsonObject1);
        kerberosConfig.putAll(confMap);
        String resourceJarPath = "E:\\datasource_resource_903\\DatasourceX\\core\\pluginLibs\\hbase\\dtHbase--release_4.3.x.jar";
//        String mytable_lf_601 = DatasourceUtils.exportSnapshot(datasourceDTO, kerberosConfig, "mytable_lf_601", "hdfs://192.168.2.10:8020/apps/hbase/data", "", resourceJarPath);
//        System.out.println(mytable_lf_601);


    }


    @Test
    public void CreateAllTable() throws Exception {
        List<Object> allTables = getAllTables();
        List<File> filestarget = new ArrayList<>();
        File corefiletarget = new File("E:\\hadoop_resource\\hdp2.7\\core-site.xml");
        File hdfsfiletarget = new File("E:\\hadoop_resource\\hdp2.7\\hdfs-site.xml");
        File hbasefiletarget = new File("E:\\hadoop_resource\\hdp2.7\\hbase-site.xml");
        filestarget.add(corefiletarget);
        filestarget.add(hdfsfiletarget);
        filestarget.add(hbasefiletarget);
        Map<String, String> confMaptarget = HdfsTest.getconf(filestarget);
        DatasourceDTO datasourceDTOtarget = new DatasourceDTO();
        datasourceDTOtarget.setDataTypeCode(DataSourceType.HBASE.getVal());
        datasourceDTOtarget.setDataType("HBase");
        datasourceDTOtarget.setDataVersion("1.x");
        JSONObject jsonObject1target = new JSONObject();
        String s1target = JSONObject.toJSONString(confMaptarget);
        jsonObject1target.put("hbaseConfig", s1target);
        datasourceDTOtarget.setDataJsonMap(jsonObject1target);
        Boolean nameSpaces1 = DatasourceUtils.createTableDescriptor(datasourceDTOtarget, new HashMap<>(), allTables, "");
        System.out.println(nameSpaces1);
    }

    @Test
    public void CreateNameSpaces() throws Exception {
        List<JSONObject> nameSpaces = getAllNameSpaces();
        List<File> filestarget = new ArrayList<>();
        File corefiletarget = new File("E:\\hadoop_resource\\hdp2.7\\core-site.xml");
        File hdfsfiletarget = new File("E:\\hadoop_resource\\hdp2.7\\hdfs-site.xml");
        File hbasefiletarget = new File("E:\\hadoop_resource\\hdp2.7\\hbase-site.xml");
        filestarget.add(corefiletarget);
        filestarget.add(hdfsfiletarget);
        filestarget.add(hbasefiletarget);
        Map<String, String> confMaptarget = HdfsTest.getconf(filestarget);
        DatasourceDTO datasourceDTOtarget = new DatasourceDTO();
        datasourceDTOtarget.setDataTypeCode(DataSourceType.HBASE.getVal());
        datasourceDTOtarget.setDataType("HBase");
        datasourceDTOtarget.setDataVersion("1.x");
        JSONObject jsonObject1target = new JSONObject();
        String s1target = JSONObject.toJSONString(confMaptarget);
        jsonObject1target.put("hbaseConfig", s1target);
        datasourceDTOtarget.setDataJsonMap(jsonObject1target);
        Boolean nameSpaces1 = DatasourceUtils.createNameSpaces(datasourceDTOtarget, new HashMap<>(), "", nameSpaces);
        System.out.println(nameSpaces1);


    }

    @Test
    public void restoreSnap() throws Exception {
        List<File> filestarget = new ArrayList<>();
        File corefiletarget = new File("E:\\hadoop_resource\\hdp2.7\\core-site.xml");
        File hdfsfiletarget = new File("E:\\hadoop_resource\\hdp2.7\\hdfs-site.xml");
        File hbasefiletarget = new File("E:\\hadoop_resource\\hdp2.7\\hbase-site.xml");
        filestarget.add(corefiletarget);
        filestarget.add(hdfsfiletarget);
        filestarget.add(hbasefiletarget);
        Map<String, String> confMaptarget = HdfsTest.getconf(filestarget);
        DatasourceDTO datasourceDTOtarget = new DatasourceDTO();
        datasourceDTOtarget.setDataTypeCode(DataSourceType.HBASE.getVal());
        datasourceDTOtarget.setDataType("HBase");
        datasourceDTOtarget.setDataVersion("1.x");
        JSONObject jsonObject1target = new JSONObject();
        String s1target = JSONObject.toJSONString(confMaptarget);
        jsonObject1target.put("hbaseConfig", s1target);
        datasourceDTOtarget.setDataJsonMap(jsonObject1target);
        Boolean nameSpaces1 = DatasourceUtils.doRestoreHbase(datasourceDTOtarget, new HashMap<>(), "mytable", "mytable_lf_601","");
        System.out.println(nameSpaces1);


    }




}
