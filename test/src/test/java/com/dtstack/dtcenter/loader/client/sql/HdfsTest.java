/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.client.sql;

import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.dto.DatasourceDTO;
import com.dsg.database.datasource.utils.DatasourceUtils;
import com.dsg.database.datasource.utils.DsgDistCPParams;
import com.dtstack.dtcenter.common.loader.common.utils.Xml2JsonUtil;
import com.dtstack.dtcenter.loader.client.BaseTest;
import com.dtstack.dtcenter.loader.client.ClientCache;
import com.dtstack.dtcenter.loader.client.IClient;
import com.dtstack.dtcenter.loader.client.IKerberos;
import com.dtstack.dtcenter.loader.dto.source.HdfsSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.kerberos.HadoopConfTool;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//import org.apache.hadoop.conf.Configuration;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 23:58 2020/2/28
 * @Description：HDFS 测试
 */
public class HdfsTest extends BaseTest {
    HdfsSourceDTO source = HdfsSourceDTO.builder()
            .defaultFS("hdfs://ns1")
            .config("{\n" +
                    "    \"dfs.ha.namenodes.ns1\": \"nn1,nn2\",\n" +
                    "    \"dfs.namenode.rpc-address.ns1.nn2\": \"**************:8020\",\n" +
                    "    \"dfs.client.failover.proxy.provider.ns1\": \"org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider\",\n" +
                    "    \"dfs.namenode.rpc-address.ns1.nn1\": \"**************:8020\",\n" +
                    "    \"dfs.nameservices\": \"ns1\"\n" +
                    "}")
            .build();

    @Test
    public void testCon() {
        IClient client = ClientCache.getClient(DataSourceType.HDFS.getVal());

        Boolean isConnected = client.testCon(source);
        if (Boolean.FALSE.equals(isConnected)) {
            throw new DtLoaderException("connection exception");
        }
    }

//    @Test
//    public void testaa() throws Exception {
//        Configuration conf = new Configuration();
//        conf.addResource(new Path("E:\\hadoop_resource\\hdp2.7\\core-site.xml"));
//        conf.addResource(new Path("E:\\hadoop_resource\\hdp2.7\\hdfs-site.xml"));
//        conf.addResource(new Path("E:\\hadoop_resource\\hdp2.7\\yarn-site.xml"));
//        conf.addResource(new Path("E:\\hadoop_resource\\hdp2.7\\mapred-site.xml"));
//        conf.set("mapreduce.app-submission.cross-platform","true");
//        conf.set("mapred.jar", "E:\\datasource_resource_903\\DatasourceX\\core\\pluginLibs\\hdfs\\dtHdfs--release_4.3.x.jar");
//        conf.set("mapreduce.job.user.name", "testuser");
//        DistCpOptions options = OptionsParser.parse(new String[]{
//                "-update",
//                "hdfs://************:8020/tmp/lf_target/",
//                "hdfs://************:8020/tmp/lf_hbase/"});
//        options.setAppend(true);
//        options.setCopyStrategy("dynamic");//动态分配策略
//        options.setTargetPathExists(true);
//        // 创建一个Configuration对象
//
//        // 切换到指定用户身份
//        UserGroupInformation ugi = UserGroupInformation.createRemoteUser("hbase");
//        ugi.doAs((PrivilegedExceptionAction<Void>) () -> {
//            // 在这里执行MapReduce任务
//            // ...
//            HdfsDistCP distcp = new HdfsDistCP(conf, options, "", null);
////        distcp.setYarnQueue(params.getYarnQueue());
//            org.apache.hadoop.mapreduce.Job job = distcp.execute("","", "", "", "");
//            System.out.println("DistCp Completed Successfully");
//            String jobID = job.getJobID().toString();
//            System.out.println(jobID);
//            return null;
//        });
//
//
//
//
//
//
//
//    }

    @Test
    public void aaa() throws Exception {
        Map<String, Object> kerberosConfig = new HashMap<>();
        kerberosConfig.put(HadoopConfTool.PRINCIPAL_FILE, "hbase.service.keytab");
        kerberosConfig.put(HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF, "krb5.conf");
        String localKerberosPath = "E:\\\\hadoop_resource\\\\hdp2.2\\";
        kerberosConfig.put("keytabPath", "E:\\\\hadoop_resource\\\\hdp2.2\\\\hbase.service.keytab");
        IKerberos kerberos = ClientCache.getKerberos(DataSourceType.HDFS.getVal());
        kerberos.prepareKerberosForConnect(kerberosConfig, localKerberosPath);
        List<File> files = new ArrayList<>();
        File corefile = new File("E:\\hadoop_resource\\hdp2.2\\core-site.xml");
        File hdfsfile = new File("E:\\hadoop_resource\\hdp2.2\\hdfs-site.xml");
        File yarnfile = new File("E:\\hadoop_resource\\hdp2.2\\yarn-site.xml");
        File mapfile = new File("E:\\hadoop_resource\\hdp2.2\\mapred-site.xml");
        files.add(corefile);
        files.add(hdfsfile);
        files.add(yarnfile);
        files.add(mapfile);
        Map<String, String> confMap = getconf(files);
        String resourceJarPath = "E:\\datasource_resource_903\\DatasourceX\\core\\pluginLibs\\hdfs\\dtHdfs--release_4.3.x.jar";
        DsgDistCPParams dsgDistCPParams = DsgDistCPParams.builder()
                .cpModel("-update")
                .sourcePath("/apps/hbase/data/data")
                .targetPath("/apps/hbase/data/data")
                .activeNNBySource("hdfs://***********:8020")
                .activeNNByTarget("hdfs://************:8020")
                .kerberosConfig(kerberosConfig).build();

     /*   // 加载 krb5.conf 文件
        File krb5File = new File("E:\\hadoop_resource\\hdp2.7\\krb5.conf");
        Properties krb5Properties = new Properties();
        krb5Properties.load(new FileInputStream(krb5File));
        String defaultRealm = krb5Properties.getProperty("default_realm");
        System.setProperty("java.security.krb5.realm", defaultRealm);
        // 获取 KDC 主机名
        String kdcHostname = krb5Properties.getProperty("kdc");
        System.setProperty("java.security.krb5.kdc", kdcHostname);
        System.setProperty("java.security.krb5.conf", "E:\\hadoop_resource\\hdp2.7\\krb5.conf");*/

        DatasourceDTO datasourceDTO = new DatasourceDTO();
        datasourceDTO.setDataTypeCode(DataSourceType.HDFS.getVal());
        datasourceDTO.setDataType("HDFS");
        datasourceDTO.setDataVersion("2.x");
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("defaultFS","hdfs://HDFSCLUSTER");
        String s1 = JSONObject.toJSONString(confMap);
        jsonObject1.put("hadoopConfig",s1);
        datasourceDTO.setDataJsonMap(jsonObject1);
//        String s = DatasourceUtils.startDistCp(datasourceDTO, kerberosConfig,"", dsgDistCPParams, resourceJarPath);
//        System.out.println(s);

    }

//    @Test
//    public void testDistCP() throws Exception {
//
//
//      /*  Map<String, Object> kerberosConfig = new HashMap<>();
//        kerberosConfig.put("java.security.krb5.conf","krb5.conf");
//        kerberosConfig.put("sasl.kerberos.service.name","hdfs");
//        String localKerberosPath = "E:\\\\hadoop_resource\\\\hdp2.7\\";
//        IKerberos kerberos = ClientCache.getKerberos(DataSourceType.HDFS.getVal());
//        // 准备 Kerberos 参数
//        kerberosConfig.put(HadoopConfTool.PRINCIPAL_FILE, "hdfs.headless.keytab");
//        kerberosConfig.put(HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF, "krb5.conf");
//        kerberos.prepareKerberosForConnect(kerberosConfig, localKerberosPath);
//        System.setProperty("HADOOP_USER_NAME", "hdfs");*/
//
//        // 准备 Kerberos 参数
//        Map<String, Object> kerberosConfig = new HashMap<>();
//        kerberosConfig.put(HadoopConfTool.PRINCIPAL_FILE, "hdfs.headless.keytab");
//        kerberosConfig.put(HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF, "krb5.conf");
//        String localKerberosPath = "E:\\\\hadoop_resource\\\\hdp2.7\\";
//        kerberosConfig.put("keytabPath", "E:\\\\hadoop_resource\\\\hdp2.7\\\\hdfs.headless.keytab");
//        IKerberos kerberos = ClientCache.getKerberos(DataSourceType.HDFS.getVal());
//        kerberos.prepareKerberosForConnect(kerberosConfig, localKerberosPath);
//        source.setKerberosConfig(kerberosConfig);
//
//
//        List<File> files = new ArrayList<>();
//        File corefile = new File("E:\\hadoop_resource\\hdp2.7\\core-site.xml");
//        File hdfsfile = new File("E:\\hadoop_resource\\hdp2.7\\hdfs-site.xml");
//        File yarnfile = new File("E:\\hadoop_resource\\hdp2.7\\yarn-site.xml");
//        File mapfile = new File("E:\\hadoop_resource\\hdp2.7\\mapred-site.xml");
//        files.add(corefile);
//        files.add(hdfsfile);
//        files.add(yarnfile);
//        files.add(mapfile);
//        Map<String, String> getconf = getconf(files);
//        org.apache.hadoop.conf.Configuration conf = new Configuration();
//        getconf.keySet().forEach(item -> {
//            if (null == getconf.get(item) || StringUtils.isEmpty(getconf.get(item))) {
//                conf.set(item, "");
//            } else {
//                conf.set(item, getconf.get(item));
//            }
//        });
//        System.setProperty("java.security.krb5.realm", "LUOFENG.COM");
//        System.setProperty("java.security.krb5.kdc", "master.hdp2.com");
//        System.setProperty("java.security.krb5.conf", "E:\\hadoop_resource\\hdp2.7\\krb5.conf");
//
//        DsgDistCPParams build = DsgDistCPParams.builder()
//                .cpModel("-update")
//                .sourcePath("/tmp/lf_source")
//                .targetPath("/tmp/lf_target")
//                .activeNNBySource("hdfs://***********:8020")
//                .activeNNByTarget("hdfs://***********:8020")
//                .kerberosConfig(kerberosConfig).build();
//        String resourceJarPath = "E:\\datasource_resource_903\\DatasourceX\\core\\pluginLibs\\hdfs\\dtHdfs--release_4.3.x.jar";
//        conf.set("mapreduce.app-submission.cross-platform", "true");
//        conf.set("ipc.client.fallback-to-simple-auth-allowed", "true");
//        conf.set("mapred.jar", resourceJarPath);
//        conf.set("hadoop.security.authentication", "kerberos");
//        conf.set("hadoop.security.kerberos.principal", "<EMAIL>");
//        conf.set("hadoop.security.kerberos.keytab", "E:\\hadoop_resource\\hdp2.7\\hdfs.headless.keytab");
//        DatasourceDTO datasourceDTO = new DatasourceDTO();
//        datasourceDTO.setDataTypeCode(DataSourceType.HDFS.getVal());
//        datasourceDTO.setDataType("HDFS");
//        datasourceDTO.setDataVersion("2.x");
///*        HdfsFileClient hdfsFileClient = new HdfsFileClient();
//        String s = hdfsFileClient.startDistcp(getconf, build, resourceJarPath);
////        String s = ClientCache.getHdfs(DataSourceType.HDFS.getVal()).startDistcp(getconf, build, resourceJarPath);
////        String s = DatasourceUtils.startDistCp(datasourceDTO, getconf, "", build, resourceJarPath);
//        System.out.println(s);*/
//
//        DistCpOptions options = OptionsParser.parse(new String[]{
//                build.getCpModel(),
//                build.getActiveNNBySource() + build.getSourcePath(),
//                build.getActiveNNByTarget() + build.getTargetPath()});
//        options.setAppend(true);
//        options.setCopyStrategy("dynamic");//动态分配策略
//        options.setTargetPathExists(true);
////        String kafkaLoginConf = KafkaUtil.writeKafkaJaas(kerberosConfig);
////        System.setProperty("java.security.auth.login.config", kafkaLoginConf);
//        UserGroupInformation.setConfiguration(conf);
//        UserGroupInformation ugi = UserGroupInformation.loginUserFromKeytabAndReturnUGI("<EMAIL>", "E:\\hadoop_resource\\hdp2.7\\hdfs.headless.keytab");
//        // Do the MapReduce job submission using the authenticated UserGroupInformation
//        ugi.doAs((java.security.PrivilegedExceptionAction<Void>) () -> {
//            try {
//                HdfsDistCP distcp = new HdfsDistCP(conf, options, "", null);
////        distcp.setYarnQueue(params.getYarnQueue());
//                org.apache.hadoop.mapreduce.Job job = distcp.execute("", "", "", "", "");
//
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            return null;
//        });
//
//
//      /*  DatasourceDTO datasourceDTO = new DatasourceDTO();
//        datasourceDTO.setDataTypeCode(DataSourceType.HDFS.getVal());
//        datasourceDTO.setDataType("HDFS");
//        datasourceDTO.setDataVersion("2.x");
////        HdfsFileClient hdfsFileClient = new HdfsFileClient();
////        String s = hdfsFileClient.startDistcp(getconf, build, resourceJarPath);
////        String s = ClientCache.getHdfs(DataSourceType.HDFS.getVal()).startDistcp(getconf, build, resourceJarPath);
//        String s = DatasourceUtils.startDistCp(datasourceDTO, getconf, "", build, resourceJarPath);
//        System.out.println(s);
//*/
//
//
//
//
//
//      /*  Configuration conf = new Configuration();
//        conf.addResource(new Path("E:\\hadoop_resource\\cdh2.6\\yarn-clientconfig\\yarn-conf\\core-site.xml"));
//        conf.addResource(new Path("E:\\hadoop_resource\\cdh2.6\\yarn-clientconfig\\yarn-conf\\hdfs-site.xml"));
//        conf.addResource(new Path("E:\\hadoop_resource\\cdh2.6\\yarn-clientconfig\\yarn-conf\\yarn-site.xml"));
//        conf.addResource(new Path("E:\\hadoop_resource\\cdh2.6\\yarn-clientconfig\\yarn-conf\\mapred-site.xml"));
//        conf.set("fs.defaultFS.name","hdfs://datalkone");
//        conf.set("mapreduce.app-submission.cross-platform","true");
//        conf.set("mapred.jar", "E:\\datasource_resource_903\\DatasourceX\\kerberos\\target\\common.loader.kerberos-1.8.2-RELEASE.jar");
//        DistCpOptions options = OptionsParser.parse(new String[]{
//                "-update",
//                "hdfs://datalkone/tmp/lf/",
//                "hdfs://datalkone/tmp/lf_data/"});
//        options.setAppend(true);
//        options.setCopyStrategy("dynamic");//动态分配策略
//        options.setTargetPathExists(true);
//        HdfsDistCP distcp = new HdfsDistCP(conf, options, "", null);
////        distcp.setYarnQueue(params.getYarnQueue());
//        org.apache.hadoop.mapreduce.Job job = distcp.execute("","", "", "", "");
//        System.out.println("DistCp Completed Successfully");
//        String jobID = job.getJobID().toString();
//        System.out.println(jobID);*/
//
//
//
//      /*  IClient client = ClientCache.getClient(DataSourceType.HDFS.getVal());
//        Boolean isConnected = client.testCon(source);
//        if (Boolean.FALSE.equals(isConnected)) {
//            throw new DtLoaderException("connection exception");
//        }*/
//    }

    public static Map<String, String> getconf(List<File> xmlFiles) throws Exception {
        Map<String, String> ll = new HashMap<>();
        Map<String, Map<String, String>> result = new HashMap<>();
        for (File file : xmlFiles) {
            Map<String, String> fileMap = null;
            if (file.getName().startsWith(".")) {
                //.开头过滤
                continue;
            }
            if (file.getName().endsWith("xml")) {
                //xml文件
                fileMap = Xml2JsonUtil.xml2map(file);
            } else if (file.getName().endsWith("json")) {
                //json文件
                String jsonStr = readFile(file);
                if (StringUtils.isBlank(jsonStr)) {
                    continue;
                }
                fileMap = (Map<String, String>) JSONObject.parseObject(jsonStr, Map.class);
            }
            if (null != fileMap) {
                result.put(file.getName(), fileMap);
            }
        }
        for (String s : result.keySet()) {
            ll.putAll(result.get(s));
        }
        return ll;
    }

    private static String readFile(File file) {
        try (FileInputStream fis = new FileInputStream(file);) {
            try (FileChannel fc = fis.getChannel()) {
                ByteBuffer bb = ByteBuffer.allocate((int) file.length());
                fc.read(bb);
                // 翻转子节流
                bb.flip();
                return new String(bb.array(), "UTF8");
            }
        } catch (FileNotFoundException e) {
            throw new DtLoaderException(String.format("file is not exist,%s", e.getMessage()), e);
        } catch (IOException e) {
            throw new DtLoaderException(String.format("File reading exception,%s", e.getMessage()), e);
        }
    }
}
