/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.client.sql;

import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.dto.DatasourceDTO;
import com.dsg.database.datasource.dto.JdbcSqlMetadataInfoDTO;
import com.dsg.database.datasource.utils.DatasourceUtils;
import com.dtstack.dtcenter.loader.cache.pool.config.PoolConfig;
import com.dtstack.dtcenter.loader.client.BaseTest;
import com.dtstack.dtcenter.loader.client.ClientCache;
import com.dtstack.dtcenter.loader.client.IClient;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.OracleSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;
import java.util.Map;

/**
 * oracle 19C 测试
 *
 * <AUTHOR>
 * date：Created in 上午10:11 2021/5/24
 * company: www.dtstack.com
 */
public class Oracle19CTest extends BaseTest {

    // 构造客户端
    private static final IClient client = ClientCache.getClient(DataSourceType.Oracle_19c.getVal());

    // 数据源信息
    private static final OracleSourceDTO source = OracleSourceDTO.builder()
            .url("************************************************")
            .username("system")
            .password("joyadata")
            .poolConfig(new PoolConfig())
            .build();

    /**
     * 测试连通性测试
     */
    @Test
    public void testCon()  {
        Boolean isConnected = client.testCon(source);
        if (Boolean.FALSE.equals(isConnected)) {
            throw new DtLoaderException("connection exception");
        }
    }

    /**
     * 根据 schema获取表
     */
    @Test
    public void getTableListBySchema()  {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().schema("TEST_WANGCHUAN_2").build();
        List<String> tableList = client.getTableListBySchema(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(tableList));
    }

    /**
     * 获取所有的schema
     */
    @Test
    public void getAllDatabases()  {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().build();
        List databases = client.getAllDatabases(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(databases));
    }

    /**
     * 获取所有的 db
     */
    @Test
    public void getPdb()  {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().build();
        List databases = client.getRootDatabases(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(databases));
    }

    /**
     * 获取表
     */
    @Test
    public void getTableAndViewList()  {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().schema("C##QJQ").build();
        List tableAndViewList = client.getTableAndViewList(source, queryDTO);
        System.out.println(tableAndViewList);
    }
    /**
     * 获取表字段详细信息
     */
    @Test
    public void getColumnMetaData()  {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().schema("C##QJQ").tableName("CC_S4CCC_TEST2").build();
        List<ColumnMetaDTO> columnMetaData = client.getColumnMetaData(source, queryDTO);
        assert CollectionUtils.isNotEmpty(columnMetaData);
    }

    /**
     * 执行查询语句测试
     */
    @Test
    public void executeQuery()  {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql("select \"EMP_ID\",\"EMP_NAME\",\"GENDER\",\"ACCOUNT\",\"ORG_ID\",\"BIRTH_DATE\",\"AGE\",\"NATIONALITY\",\"PROVINCE\",\"CITY\",\"EMAIL\",\"PHONE\",\"BEGIN_DATE\",\"REMARK\",\"CREATE_TIME\",\"UPDATE_TIME\",\"SAL\" from \"C##QJQ\".\"EMP2\"").build();
        List<Map<String, Object>> result = client.executeQuery(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(result));
    }


    @Test
    public void getByExecuteQuery() {

   /*     SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql(sql).limit(5).build();
        List list = client.executeQuery(source, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(list));*/

        DatasourceDTO datasourceDTO=DatasourceDTO.builder()
                .dataVersion("19c")
                .dataType("Oracle")
                .dataJson("************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")
                .dataTypeCode(DataSourceType.Oracle_19c.getVal())
                .currentView(true)
                .pageNum(0)
                .previewNum(15)
                .build();
        JSONObject preview = DatasourceUtils.preview(datasourceDTO);
        System.out.println(preview);


    }
}
