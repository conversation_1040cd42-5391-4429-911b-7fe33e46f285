/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.client.common;

import com.dtstack.dtcenter.common.loader.common.utils.AddressUtil;
import com.dtstack.dtcenter.loader.client.testutil.ReflectUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;

import java.net.InetAddress;
import java.util.List;
import java.util.Set;

/**
 * 地址工具类测试
 *
 * <AUTHOR>
 * date：Created in 上午10:31 2021/6/4
 * company: www.dtstack.com
 */
public class AddressUtilTest {

    /**
     * 获取本地 ip 地址
     */
    @Test
    public void resolveLocalAddressesTest() {
        Set<InetAddress> addresses = AddressUtil.resolveLocalAddresses();
        Assert.assertTrue(CollectionUtils.isNotEmpty(addresses));
    }

    /**
     * 判断是否是本机地址
     */
    @Test
    public void isSpecialIpTest() {
        Assert.assertTrue(ReflectUtil.invokeStaticMethod(AddressUtil.class, Boolean.class, "isSpecialIp", new Class[]{String.class}, ":"));
        Assert.assertTrue(ReflectUtil.invokeStaticMethod(AddressUtil.class, Boolean.class, "isSpecialIp", new Class[]{String.class}, "127.0.0.1"));
        Assert.assertTrue(ReflectUtil.invokeStaticMethod(AddressUtil.class, Boolean.class, "isSpecialIp", new Class[]{String.class}, "***********"));
        Assert.assertTrue(ReflectUtil.invokeStaticMethod(AddressUtil.class, Boolean.class, "isSpecialIp", new Class[]{String.class}, "***************"));
        Assert.assertFalse(ReflectUtil.invokeStaticMethod(AddressUtil.class, Boolean.class, "isSpecialIp", new Class[]{String.class}, "*******"));
    }

    /**
     * 获取本机的第一个 ip
     */
    @Test
    public void getOneIP() {
        String oneIP = AddressUtil.getOneIP();
        Assert.assertTrue(StringUtils.isNotBlank(oneIP));
    }

    /**
     * 获取本地 ip
     */
    @Test
    public void resolveLocalIps() {
        List<String> ips = AddressUtil.resolveLocalIps();
        Assert.assertTrue(CollectionUtils.isNotEmpty(ips));
    }

    /**
     * telnet ip port
     */
    @Test
    public void telnet() {
        Assert.assertTrue(AddressUtil.telnet("**************", 9023));
        Assert.assertFalse(AddressUtil.telnet("0.0.0.0", 8080));
    }

    /**
     * ping ip
     */
    @Test
    public void ping() {
        Assert.assertTrue(AddressUtil.ping("**************"));
        Assert.assertFalse(AddressUtil.ping("*******"));
    }

    /**
     * 校验 ip 是否是 '0.0.0.0', '127.0.0.1'
     */
    @Test
    public void checkAddrIsLocal() {
        Assert.assertTrue(AddressUtil.checkAddrIsLocal("0.0.0.0"));
        Assert.assertFalse(AddressUtil.ping("*******"));
    }

    /**
     * 检查服务是否相同：ip相同，端口相同
     */
    @Test
    public void checkServiceIsSame() throws Exception {
        Assert.assertTrue(AddressUtil.checkServiceIsSame("**************", 80, "**************", 80));
        Assert.assertFalse(AddressUtil.checkServiceIsSame("**************", 80, "**************", 81));
    }
}
