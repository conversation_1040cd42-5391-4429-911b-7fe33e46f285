<?xml version="1.0" encoding="UTF-8"?>

<!--Autogenerated by Cloudera Manager-->
<configuration>
  <property>
    <name>ssl.client.truststore.type</name>
    <value>jks</value>
  </property>
  <property>
    <name>ssl.client.truststore.location</name>
    <value>/Users/<USER>/projects/dt-center-common-loader/test/src/test/resources/ssl/cm-auto-global_truststore.jks</value>
  </property>
  <property>
    <name>ssl.client.truststore.password</name>
    <value>XfscZOW22LMAgrrZxo8MIhf2L3MTodv6kXpvBt2eg7X</value>
  </property>
  <property>
    <name>ssl.client.truststore.reload.interval</name>
    <value>10000</value>
  </property>
</configuration>
