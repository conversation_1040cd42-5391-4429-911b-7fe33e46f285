# Configuration snippets may be placed in this directory as well
includedir /etc/krb5.conf.d/

[logging]
 default = FILE:/var/log/krb5libs.log
 kdc = FILE:/var/log/krb5kdc.log
 admin_server = FILE:/var/log/kadmind.log

[libdefaults]
 dns_lookup_realm = false
 #访问凭证的有效时间，默认是24小时
 ticket_lifetime = 24h
 renew_lifetime = 7d
 forwardable = true
 rdns = false
 pkinit_anchors = FILE:/etc/pki/tls/certs/ca-bundle.crt
 default_realm = DTSTACK.COM
 # default_ccache_name = KEYRING:persistent:%{uid}

[realms]
DTSTACK.COM = {
 kdc = krbe2:88
 admin_server = krbe2:749
}

[domain_realm]
.dtstack.com = DTSTACK.COM
dtstack.com = DTSTACK.COM