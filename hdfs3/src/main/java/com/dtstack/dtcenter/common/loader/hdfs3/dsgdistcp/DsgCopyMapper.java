package com.dtstack.dtcenter.common.loader.hdfs3.dsgdistcp;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileChecksum;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Mapper;
import org.apache.hadoop.tools.CopyListingFileStatus;
import org.apache.hadoop.tools.DistCpOptions.FileAttribute;
import org.apache.hadoop.tools.mapred.RetriableDirectoryCreateCommand;
import org.apache.hadoop.tools.mapred.RetriableFileCopyCommand.CopyReadException;
import org.apache.hadoop.util.StringUtils;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.EnumSet;

/**
 * @描述
 * @创建人 lf
 * @创建时间 2022/4/27
 * @create 2022-04-27-14:38
 */
public class DsgCopyMapper extends Mapper<Text, CopyListingFileStatus, Text, Text> {
    private static Log LOG = LogFactory.getLog(DsgCopyMapper.class);
    private Configuration conf;
    private boolean syncFolders = false;
    private boolean ignoreFailures = false;
    private boolean skipCrc = false;
    private boolean overWrite = false;
    private boolean append = false;
    private EnumSet<DsgDistCpOptions.FileAttribute> preserve = EnumSet.noneOf(DsgDistCpOptions.FileAttribute.class);
    private FileSystem targetFS = null;
    private Path targetWorkPath = null;
    private boolean directWrite = false;

    public DsgCopyMapper() {
    }

    public void setup(Context context) throws IOException, InterruptedException {
        this.conf = context.getConfiguration();
        this.syncFolders = this.conf.getBoolean(DsgDistCpOptionSwitch.SYNC_FOLDERS.getConfigLabel(), false);
        this.ignoreFailures = this.conf.getBoolean(DsgDistCpOptionSwitch.IGNORE_FAILURES.getConfigLabel(), false);
        this.skipCrc = this.conf.getBoolean(DsgDistCpOptionSwitch.SKIP_CRC.getConfigLabel(), false);
        this.overWrite = this.conf.getBoolean(DsgDistCpOptionSwitch.OVERWRITE.getConfigLabel(), false);
        this.append = this.conf.getBoolean(DsgDistCpOptionSwitch.APPEND.getConfigLabel(), false);
        this.preserve = DsgDistCpUtils2.unpackAttributes(this.conf.get(DsgDistCpOptionSwitch.PRESERVE_STATUS.getConfigLabel()));
        this.directWrite = conf.getBoolean(
                DsgDistCpOptionSwitch.DIRECT_WRITE.getConfigLabel(), false);
        this.targetWorkPath = new Path(this.conf.get("distcp.target.work.path"));
        Path targetFinalPath = new Path(this.conf.get("distcp.target.final.path"));
        this.targetFS = targetFinalPath.getFileSystem(this.conf);
        if (this.targetFS.exists(targetFinalPath) && this.targetFS.isFile(targetFinalPath)) {
            this.overWrite = true;
        }

        if (this.conf.get("distcp.keystore.resource") != null) {
            this.initializeSSLConf(context);
        }

    }

    private void initializeSSLConf(Context context) throws IOException {
        LOG.info("Initializing SSL configuration");
        String workDir = this.conf.get("mapreduce.job.local.dir") + "/work";
        Path[] cacheFiles = context.getLocalCacheFiles();
        Configuration sslConfig = new Configuration(false);
        String sslConfFileName = this.conf.get("distcp.keystore.resource");
        Path sslClient = this.findCacheFile(cacheFiles, sslConfFileName);
        if (sslClient == null) {
            LOG.warn("SSL Client config file not found. Was looking for " + sslConfFileName + " in " + Arrays.toString(cacheFiles));
        } else {
            sslConfig.addResource(sslClient);
            String trustStoreFile = this.conf.get("ssl.client.truststore.location");
            Path trustStorePath = this.findCacheFile(cacheFiles, trustStoreFile);
            sslConfig.set("ssl.client.truststore.location", trustStorePath.toString());
            String keyStoreFile = this.conf.get("ssl.client.keystore.location");
            Path keyStorePath = this.findCacheFile(cacheFiles, keyStoreFile);
            sslConfig.set("ssl.client.keystore.location", keyStorePath.toString());

            try {
                FileOutputStream out = new FileOutputStream(workDir + "/" + sslConfFileName);

                try {
                    sslConfig.writeXml(out);
                } finally {
                    out.close();
                }

                this.conf.set("dfs.https.client.keystore.resource", sslConfFileName);
            } catch (IOException var16) {
                LOG.warn("Unable to write out the ssl configuration. Will fall back to default ssl-client.xml in class path, if there is one", var16);
            }

        }
    }

    private Path findCacheFile(Path[] cacheFiles, String fileName) {
        if (cacheFiles != null && cacheFiles.length > 0) {
            Path[] arr$ = cacheFiles;
            int len$ = cacheFiles.length;

            for (int i$ = 0; i$ < len$; ++i$) {
                Path file = arr$[i$];
                if (file.getName().equals(fileName)) {
                    return file;
                }
            }
        }

        return null;
    }

    public void map(Text relPath, CopyListingFileStatus sourceFileStatus, Context context) throws IOException, InterruptedException {
        Path sourcePath = sourceFileStatus.getPath();

        if (LOG.isDebugEnabled()) {
            LOG.debug("DistCpMapper::map(): Received " + sourcePath + ", " + relPath);
        }

        Path target = new Path(this.targetWorkPath.makeQualified(this.targetFS.getUri(), this.targetFS.getWorkingDirectory()) + relPath.toString());
        EnumSet<DsgDistCpOptions.FileAttribute> fileAttributes = getFileAttributeSettings(context);
        boolean preserveRawXattrs = context.getConfiguration().getBoolean("distcp.preserve.rawxattrs", false);
        String description = "Copying " + sourcePath + " to " + target;
        context.setStatus(description);
//        LOG.info(description);

        try {
            CopyListingFileStatus sourceCurrStatus = null;
            FileSystem sourceFS = null;
            try {
                sourceFS = sourcePath.getFileSystem(this.conf);
                sourceCurrStatus = DsgDistCpUtils2.toCopyListingFileStatus(sourceFS, sourceFS.getFileStatus(sourcePath), fileAttributes.contains(FileAttribute.ACL), fileAttributes.contains(FileAttribute.XATTR));
            } catch (FileNotFoundException var12) {
                throw new IOException(new CopyReadException(var12));
            }

            FileStatus targetStatus = null;

            try {
                targetStatus = this.targetFS.getFileStatus(target);
            } catch (FileNotFoundException var13) {
                if (LOG.isDebugEnabled()) {
                    LOG.debug("Path could not be found: " + target, var13);
                }
            }
            FileStatus fileStatus = sourceFS.getFileStatus(sourceCurrStatus.getPath());
            if (targetStatus != null && targetStatus.isDirectory() != sourceCurrStatus.isDirectory()) {
                throw new IOException("Can't replace " + target + ". Target is " + this.getFileType(targetStatus) + ", Source is " + this.getFileType(fileStatus));
            }

            if (sourceCurrStatus.isDirectory()) {
                this.createTargetDirsWithRetry(description, target, context);
                return;
            }

            DsgCopyMapper.FileAction action = this.checkUpdate(sourceFS, fileStatus, target);
            if (action == DsgCopyMapper.FileAction.SKIP) {
                LOG.info("Skipping copy of " + sourceCurrStatus.getPath() + " to " + target);
                updateSkipCounters(context, fileStatus);
                context.write(null, new Text("SKIP: " + sourceCurrStatus.getPath()));
            } else {
                this.copyFileWithRetry(description, fileStatus, target, context, action, fileAttributes);
            }
            DsgDistCpUtils2.preserve(targetFS, target, sourceCurrStatus, fileAttributes);
        } catch (IOException var14) {
            var14.printStackTrace();
            this.handleFailures(var14, sourceFileStatus, target, context);
        }


    }

    private String getFileType(FileStatus fileStatus) {
        return fileStatus == null ? "N/A" : (fileStatus.isDirectory() ? "dir" : "file");
    }

    private static EnumSet<DsgDistCpOptions.FileAttribute> getFileAttributeSettings(Context context) {
        String attributeString = context.getConfiguration().get(DsgDistCpOptionSwitch.PRESERVE_STATUS.getConfigLabel());
        return DsgDistCpUtils2.unpackAttributes(attributeString);
    }

    private void copyFileWithRetry(String description, FileStatus sourceFileStatus, Path target, Context context, DsgCopyMapper.FileAction action, EnumSet<DsgDistCpOptions.FileAttribute> fileAttributes) throws IOException {
        long bytesCopied;
        try {
            DsgRetriableFileCopyCommand dsgRetriableFileCopyCommand = new DsgRetriableFileCopyCommand(this.skipCrc, description, action, directWrite);
            bytesCopied = (Long) dsgRetriableFileCopyCommand.execute(new Object[]{sourceFileStatus, target, context, fileAttributes});
        } catch (Exception var10) {
            context.setStatus("Copy Failure: " + sourceFileStatus.getPath());
            throw new IOException("File copy failed: " + sourceFileStatus.getPath() + " --> " + target, var10);
        }

        incrementCounter(context, DsgCopyMapper.Counter.BYTESEXPECTED, sourceFileStatus.getLen());
        incrementCounter(context, DsgCopyMapper.Counter.BYTESCOPIED, bytesCopied);
        incrementCounter(context, DsgCopyMapper.Counter.COPY, 1L);
    }

    private void createTargetDirsWithRetry(String description, Path target, Context context) throws IOException {
        try {
            (new RetriableDirectoryCreateCommand(description)).execute(new Object[]{target, context});
        } catch (Exception var5) {
            throw new IOException("mkdir failed for " + target, var5);
        }

        incrementCounter(context, DsgCopyMapper.Counter.COPY, 1L);
    }

    private static void updateSkipCounters(Context context, FileStatus sourceFile) {
        incrementCounter(context, DsgCopyMapper.Counter.SKIP, 1L);
        incrementCounter(context, DsgCopyMapper.Counter.BYTESSKIPPED, sourceFile.getLen());
    }

    private void handleFailures(IOException exception, CopyListingFileStatus sourceFileStatus, Path target, Context context) throws IOException, InterruptedException {
        LOG.error("Failure in copying " + sourceFileStatus.getPath() + " to " + target, exception);
        if (this.ignoreFailures && exception.getCause() instanceof CopyReadException) {
            incrementCounter(context, DsgCopyMapper.Counter.FAIL, 1L);
            incrementCounter(context, DsgCopyMapper.Counter.BYTESFAILED, sourceFileStatus.getLen());
            context.write(null, new Text("FAIL: " + sourceFileStatus.getPath() + " - " + StringUtils.stringifyException(exception)));
        } else {
            throw exception;
        }
    }

    private static void incrementCounter(Context context, DsgCopyMapper.Counter counter, long value) {
        context.getCounter(counter).increment(value);
    }

    private DsgCopyMapper.FileAction checkUpdate(FileSystem sourceFS, FileStatus source, Path target) throws IOException {
        FileStatus targetFileStatus;
        try {
            targetFileStatus = this.targetFS.getFileStatus(target);
        } catch (FileNotFoundException var8) {
            return DsgCopyMapper.FileAction.OVERWRITE;
        }

        if (targetFileStatus != null && !this.overWrite) {
            if (this.canSkip(sourceFS, source, targetFileStatus)) {
                return DsgCopyMapper.FileAction.SKIP;
            }

            if (this.append) {
                long targetLen = targetFileStatus.getLen();
                if (targetLen < source.getLen()) {
                    FileChecksum sourceChecksum = sourceFS.getFileChecksum(source.getPath(), targetLen);
                    if (sourceChecksum != null && sourceChecksum.equals(this.targetFS.getFileChecksum(target))) {
                        return DsgCopyMapper.FileAction.APPEND;
                    }
                }
            }
        }

        return DsgCopyMapper.FileAction.OVERWRITE;
    }

    private boolean canSkip(FileSystem sourceFS, FileStatus source, FileStatus target) throws IOException {
        if (!this.syncFolders) {
            return true;
        } else {
            boolean sameLength = target.getLen() == source.getLen();
            boolean sameBlockSize = source.getBlockSize() == target.getBlockSize() || !this.preserve.contains(FileAttribute.BLOCKSIZE);
            if (sameLength && sameBlockSize) {
                return this.skipCrc || DsgDistCpUtils2.checksumsAreEqual(sourceFS, source.getPath(), (FileChecksum) null, this.targetFS, target.getPath());
            } else {
                return false;
            }
        }
    }

    static enum FileAction {
        SKIP,
        APPEND,
        OVERWRITE;

        private FileAction() {
        }
    }

    public static enum Counter {
        COPY,
        SKIP,
        FAIL,
        BYTESCOPIED,
        BYTESEXPECTED,
        BYTESFAILED,
        BYTESSKIPPED;

        private Counter() {
        }
    }
}
