package com.dtstack.dtcenter.common.loader.hdfs3.dsgdistcp;

import com.google.common.annotations.VisibleForTesting;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.*;
import org.apache.hadoop.fs.permission.FsPermission;
import org.apache.hadoop.io.IOUtils;
import org.apache.hadoop.mapreduce.Mapper;
import org.apache.hadoop.tools.util.DistCpUtils;
import org.apache.hadoop.tools.util.RetriableCommand;

import java.io.BufferedOutputStream;
import java.io.Closeable;
import java.io.IOException;
import java.io.OutputStream;
import java.util.EnumSet;

/**
 * @描述
 * @创建人 lf
 * @创建时间 2022/4/27
 * @create 2022-04-27-15:50
 */
public class DsgRetriableFileCopyCommand extends RetriableCommand {
    private static Log LOG = LogFactory.getLog(DsgRetriableFileCopyCommand.class);
    private static int BUFFER_SIZE = 8192;
    private boolean skipCrc;
    private DsgCopyMapper.FileAction action;
    private boolean directWrite = false;
    public DsgRetriableFileCopyCommand(String description, DsgCopyMapper.FileAction action) {
        super(description);
        this.skipCrc = false;
        this.action = action;

    }

    public DsgRetriableFileCopyCommand(boolean skipCrc, String description, DsgCopyMapper.FileAction action) {
        this(description, action);
        this.skipCrc = skipCrc;
    }
    public DsgRetriableFileCopyCommand(boolean skipCrc, String description,
                                       DsgCopyMapper.FileAction action, boolean directWrite) {
        this(skipCrc, description, action);
        this.directWrite = directWrite;
    }

    protected Object doExecute(Object... arguments) throws Exception {
//        LOG.error("进入到DsgRetriableFileCopyCommand");
        assert arguments.length == 4 : "Unexpected argument list.";

        FileStatus source = (FileStatus)arguments[0];

        assert !source.isDirectory() : "Unexpected file-status. Expected file.";

        Path target = (Path)arguments[1];
        Mapper.Context context = (Mapper.Context)arguments[2];
        EnumSet<DsgDistCpOptions.FileAttribute> fileAttributes = (EnumSet)arguments[3];
        return this.doCopy(source, target, context, fileAttributes);
    }

    private long doCopy(FileStatus sourceFileStatus, Path target, Mapper.Context context, EnumSet<DsgDistCpOptions.FileAttribute> fileAttributes) throws IOException {
        boolean toAppend = this.action == DsgCopyMapper.FileAction.APPEND;
        final boolean useTempTarget =  false;
        Path targetPath = useTempTarget ? getTmpFile(target, context) : target;
//        LOG.error("进入到docopy得备份"+target);
//        Path targetPath = toAppend ? target : this.getTmpFile(target, context);
        Configuration configuration = context.getConfiguration();
        FileSystem targetFS = target.getFileSystem(configuration);

        long var16;
        try {
            if (LOG.isDebugEnabled()) {
                LOG.debug("Copying " + sourceFileStatus.getPath() + " to " + target);
                LOG.debug("Target file path: " + targetPath);
            }

            Path sourcePath = sourceFileStatus.getPath();
            FileSystem sourceFS = sourcePath.getFileSystem(configuration);
            FileChecksum sourceChecksum = fileAttributes.contains(DsgDistCpOptions.FileAttribute.CHECKSUMTYPE) ? sourceFS.getFileChecksum(sourcePath) : null;
            long offset = this.action == DsgCopyMapper.FileAction.APPEND ? targetFS.getFileStatus(target).getLen() : 0L;
            long bytesRead = this.copyToFile(targetPath, targetFS, sourceFileStatus, offset, context, fileAttributes, sourceChecksum);
            this.compareFileLengths(sourceFileStatus, targetPath, configuration, bytesRead + offset);
            if (bytesRead != 0L && !this.skipCrc) {
                this.compareCheckSums(sourceFS, sourceFileStatus.getPath(), sourceChecksum, targetFS, targetPath);
            }

       /*     if (!toAppend) {
                this.promoteTmpToTarget(targetPath, target, targetFS);
            }*/

            var16 = bytesRead;
        } finally {
         /*   if (!toAppend && targetFS.exists(targetPath)) {
                targetFS.delete(targetPath, false);
            }*/

        }

        return var16;
    }

    private Options.ChecksumOpt getChecksumOpt(EnumSet<DsgDistCpOptions.FileAttribute> fileAttributes, FileChecksum sourceChecksum) {
        return fileAttributes.contains(DsgDistCpOptions.FileAttribute.CHECKSUMTYPE) && sourceChecksum != null ? sourceChecksum.getChecksumOpt() : null;
    }

    private long copyToFile(Path targetPath, FileSystem targetFS, FileStatus sourceFileStatus, long sourceOffset, Mapper.Context context, EnumSet<DsgDistCpOptions.FileAttribute> fileAttributes, FileChecksum sourceChecksum) throws IOException {
        FsPermission permission = FsPermission.getFileDefault().applyUMask(FsPermission.getUMask(targetFS.getConf()));
        BufferedOutputStream outStream;
//        LOG.error("进入到copyToFile"+targetFS.exists(targetPath)+targetPath + action.name());
        try {
            if (this.action == DsgCopyMapper.FileAction.OVERWRITE) {
                short repl = getReplicationFactor(fileAttributes, sourceFileStatus, targetFS, targetPath);
//                LOG.error("路径存在OVERWRITE创建"+repl);
                long blockSize = getBlockSize(fileAttributes, sourceFileStatus, targetFS, targetPath);
//                LOG.error("路径存在OVERWRITE创建"+blockSize);
//                LOG.error("路径存在OVERWRITE创建"+permission);
//                LOG.error("路径存在OVERWRITE创建"+EnumSet.of(CreateFlag.CREATE, CreateFlag.OVERWRITE));
                FSDataOutputStream out = targetFS.create(targetPath, permission, EnumSet.of(CreateFlag.CREATE, CreateFlag.OVERWRITE), BUFFER_SIZE, repl, blockSize, context, this.getChecksumOpt(fileAttributes, sourceChecksum));
//                LOG.error("路径存在OVERWRITE创建"+out.toString());
                outStream = new BufferedOutputStream(out);
            } else {
                outStream = new BufferedOutputStream(targetFS.append(targetPath, BUFFER_SIZE));
            }
        }catch (Exception e){
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }


        return this.copyBytes(sourceFileStatus, sourceOffset, outStream, BUFFER_SIZE, context);
    }

    private void compareFileLengths(FileStatus sourceFileStatus, Path target, Configuration configuration, long targetLen) throws IOException {
        Path sourcePath = sourceFileStatus.getPath();
        FileSystem fs = sourcePath.getFileSystem(configuration);
        if (fs.getFileStatus(sourcePath).getLen() != targetLen) {
            throw new IOException("Mismatch in length of source:" + sourcePath + " and target:" + target);
        }
    }

    private void compareCheckSums(FileSystem sourceFS, Path source, FileChecksum sourceChecksum, FileSystem targetFS, Path target) throws IOException {
        if (!DsgDistCpUtils2.checksumsAreEqual(sourceFS, source, sourceChecksum, targetFS, target)) {
            StringBuilder errorMessage = (new StringBuilder("Check-sum mismatch between ")).append(source).append(" and ").append(target).append(".");
            if (sourceFS.getFileStatus(source).getBlockSize() != targetFS.getFileStatus(target).getBlockSize()) {
                errorMessage.append(" Source and target differ in block-size.").append(" Use -pb to preserve block-sizes during copy.").append(" Alternatively, skip checksum-checks altogether, using -skipCrc.").append(" (NOTE: By skipping checksums, one runs the risk of masking data-corruption during file-transfer.)");
            }

            throw new IOException(errorMessage.toString());
        }
    }

    private void promoteTmpToTarget(Path tmpTarget, Path target, FileSystem fs) throws IOException {
        if (fs.exists(target) && !fs.delete(target, false) || !fs.exists(target.getParent()) && !fs.mkdirs(target.getParent()) || !fs.rename(tmpTarget, target)) {
            throw new IOException("Failed to promote tmp-file:" + tmpTarget + " to: " + target);
        }
    }

    private Path getTmpFile(Path target, Mapper.Context context) {
        Path targetWorkPath = new Path(context.getConfiguration().get("distcp.target.work.path"));
        Path root = target.equals(targetWorkPath) ? targetWorkPath.getParent() : targetWorkPath;
        LOG.info("Creating temp file: " + new Path(root, ".distcp.tmp." + context.getTaskAttemptID().toString()));
        return new Path(root, ".distcp.tmp." + context.getTaskAttemptID().toString());
    }

    @VisibleForTesting
    long copyBytes(FileStatus sourceFileStatus, long sourceOffset, OutputStream outStream, int bufferSize, Mapper.Context context) throws IOException {
        Path source = sourceFileStatus.getPath();
        byte[] buf = new byte[bufferSize];
        DsgThrottledInputStream inStream = null;
        long totalBytesRead = 0L;

        try {
            inStream = getInputStream(source, context.getConfiguration());

            for(int bytesRead = readBytes(inStream, buf, sourceOffset); bytesRead >= 0; bytesRead = readBytes(inStream, buf, sourceOffset)) {
                totalBytesRead += (long)bytesRead;
                if (this.action == DsgCopyMapper.FileAction.APPEND) {
                    sourceOffset += (long)bytesRead;
                }

                outStream.write(buf, 0, bytesRead);
                this.updateContextStatus(totalBytesRead, context, sourceFileStatus);
            }

            outStream.close();
            outStream = null;
        } finally {
            IOUtils.cleanup(LOG, new Closeable[]{outStream, inStream});
        }

        return totalBytesRead;
    }

    private void updateContextStatus(long totalBytesRead, Mapper.Context context, FileStatus sourceFileStatus) {
        StringBuilder message = new StringBuilder(DsgDistCpUtils2.getFormatter().format((double)((float)totalBytesRead * 100.0F / (float)sourceFileStatus.getLen())));
        message.append("% ").append(this.description).append(" [").append(DsgDistCpUtils2.getStringDescriptionFor(totalBytesRead)).append('/').append(DistCpUtils.getStringDescriptionFor(sourceFileStatus.getLen())).append(']');
        context.setStatus(message.toString());
    }

    private static int readBytes(DsgThrottledInputStream inStream, byte[] buf, long position) throws IOException {
        try {
            return position == 0L ? inStream.read(buf) : inStream.read(position, buf, 0, buf.length);
        } catch (IOException var5) {
            throw new DsgRetriableFileCopyCommand.CopyReadException(var5);
        }
    }

    private static DsgThrottledInputStream getInputStream(Path path, Configuration conf) throws IOException {
        try {
            FileSystem fs = path.getFileSystem(conf);
            long bandwidthMB = (long)conf.getInt("distcp.map.bandwidth.mb", 100);
            FSDataInputStream in = fs.open(path);
            return new DsgThrottledInputStream(in, bandwidthMB * 1024L * 1024L);
        } catch (IOException var6) {
            throw new DsgRetriableFileCopyCommand.CopyReadException(var6);
        }
    }

    private static short getReplicationFactor(EnumSet<DsgDistCpOptions.FileAttribute> fileAttributes, FileStatus sourceFile, FileSystem targetFS, Path tmpTargetPath) {
        return fileAttributes.contains(DsgDistCpOptions.FileAttribute.REPLICATION) ? sourceFile.getReplication() : targetFS.getDefaultReplication(tmpTargetPath);
    }

    private static long getBlockSize(EnumSet<DsgDistCpOptions.FileAttribute> fileAttributes, FileStatus sourceFile, FileSystem targetFS, Path tmpTargetPath) {
        boolean preserve = fileAttributes.contains(DsgDistCpOptions.FileAttribute.BLOCKSIZE) || fileAttributes.contains(DsgDistCpOptions.FileAttribute.CHECKSUMTYPE);
        return preserve ? sourceFile.getBlockSize() : targetFS.getDefaultBlockSize(tmpTargetPath);
    }

    public static class CopyReadException extends IOException {
        public CopyReadException(Throwable rootCause) {
            super(rootCause);
        }
    }
}
