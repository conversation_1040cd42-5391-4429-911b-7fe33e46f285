package com.dtstack.dtcenter.common.loader.hdfs3.dsgdistcp;

import com.google.common.annotations.VisibleForTesting;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.conf.Configured;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Cluster;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.JobSubmissionFiles;
import org.apache.hadoop.tools.CopyListing;
import org.apache.hadoop.tools.CopyListing.AclsNotSupportedException;
import org.apache.hadoop.tools.CopyListing.XAttrsNotSupportedException;
import org.apache.hadoop.tools.DistCpContext;
import org.apache.hadoop.tools.DistCpOptions;
import org.apache.hadoop.tools.OptionsParser;
import org.apache.hadoop.tools.mapred.CopyOutputFormat;
import org.apache.hadoop.tools.util.DistCpUtils;
import org.apache.hadoop.util.Tool;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Random;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: HdfsDistCP
 * @Function: TODO
 * @Date: 2023/5/24 10:28
 */
public class HdfsDistCP extends Configured implements Tool {
    public static final int SHUTDOWN_HOOK_PRIORITY = 30;
    private static final Log LOG = LogFactory.getLog(HdfsDistCP.class);
    private DistCpOptions inputOptions;
    private Path metaFolder;
    private static final String PREFIX = "_distcp";
    private static final String WIP_PREFIX = "._WIP_";
    private static final String DISTCP_DEFAULT_XML = "distcp-default.xml";
    public static final Random rand = new Random();
    private boolean submitted;
    private FileSystem jobFS;
    private String callbackUrl;
    private String dsgTaskType;
    private DistCpContext distCpContext;

    private String yarnQueue;

    public HdfsDistCP(Configuration configuration, DistCpOptions inputOptions, String callbackUrl, String dsgTaskType) throws Exception {
        Configuration config = new Configuration(configuration);
        config.addResource("distcp-default.xml");
        this.setConf(config);
        this.inputOptions = inputOptions;
        this.metaFolder = this.createMetaFolderPath();
        this.callbackUrl = callbackUrl;
        this.dsgTaskType = dsgTaskType;
        this.distCpContext=new DistCpContext(inputOptions);
    }

    @VisibleForTesting
    public HdfsDistCP() {
    }

    @Override
    public int run(String[] argv) {
        if (argv.length < 1) {
            OptionsParser.usage();
            return -1;
        } else {
            try {
                this.inputOptions = OptionsParser.parse(argv);
                this.setTargetPathExists();
                LOG.info("Input Options: " + this.inputOptions);
            } catch (Throwable var8) {
                LOG.error("Invalid arguments: ", var8);
                System.err.println("Invalid arguments: " + var8.getMessage());
                OptionsParser.usage();
                return -1;
            }

            try {
                this.execute();
                return 0;
            } catch (InvalidInputException var3) {
                LOG.error("Invalid input: ", var3);
                return -1;
            } catch (DuplicateFileException var4) {
                LOG.error("Duplicate files in input path: ", var4);
                return -2;
            } catch (AclsNotSupportedException var5) {
                LOG.error("ACLs not supported on at least one file system: ", var5);
                return -3;
            } catch (XAttrsNotSupportedException var6) {
                LOG.error("XAttrs not supported on at least one file system: ", var6);
                return -4;
            } catch (Exception var7) {
                LOG.error("Exception encountered ", var7);
                return -999;
            }
        }
    }

    public Job execute() throws Exception {
        assert this.inputOptions != null;

        assert this.getConf() != null;

        Job job = null;

        try {
            synchronized (this) {
                this.metaFolder = this.createMetaFolderPath();
                this.jobFS = this.metaFolder.getFileSystem(this.getConf());
                job = this.createJob();
            }

            this.createInputFileListing(job);
            job.submit();
            this.submitted = true;
        } finally {
            if (!this.submitted) {
                this.cleanup();
            }

        }

        String jobID = job.getJobID().toString();
        job.getConfiguration().set("distcp.job.id", jobID);
        LOG.info("DistCp job-id: " + jobID);
        if (this.inputOptions.shouldBlock() && !job.waitForCompletion(true)) {
            throw new IOException("DistCp failure: Job " + jobID + " has failed: " + job.getStatus().getFailureInfo());
        } else {
            return job;
        }
    }

    public Job execute(String nodeIp, String nodePort, String dsgTaskResInfoId, String dsgTaskResId, String webToken) throws Exception {
        assert this.inputOptions != null;

        assert this.getConf() != null;

        Job job = null;

        try {
            synchronized (this) {
                this.metaFolder = this.createMetaFolderPath();
                this.jobFS = this.metaFolder.getFileSystem(this.getConf());
                job = this.createJob();
            }

            this.createInputFileListing(job);
            job.submit();
            this.submitted = true;
        } finally {
            if (!this.submitted) {
                this.cleanup();
            }

        }


        String jobID = job.getJobID().toString();
//        String geturl = DsgHttpUtil.geturl(nodeIp, Integer.valueOf(nodePort), CallBackUrlPath.callbakUrl);
//        DsgHttpUtil.sendGet(geturl, jobID, "", dsgTaskResInfoId, dsgTaskResId, webToken);
        job.getConfiguration().set("distcp.job.id", jobID);
        LOG.info("DistCp job-id: " + jobID);
        DistCpContext cpContext = new DistCpContext(this.inputOptions);
        if (cpContext.shouldBlock()) {
            if ( !job.waitForCompletion(true)) {
                throw new IOException("DistCp failure: Job " + jobID + " has failed: " + job.getStatus().getFailureInfo());
            }
        }
        return job;
    }

    private void setTargetPathExists() throws IOException {
        Path target = this.inputOptions.getTargetPath();
        FileSystem targetFS = target.getFileSystem(this.getConf());
        boolean targetExists = targetFS.exists(target);
        this.distCpContext.setTargetPathExists(targetExists);
        this.getConf().setBoolean("distcp.target.path.exists", targetExists);
    }

    private Job createJob() throws IOException {
        String jobName = "dsgcp";
        String userChosenName = this.getConf().get("mapreduce.job.name");
        if (userChosenName != null) {
            jobName = jobName + ": " + userChosenName;
        }
        Job job = Job.getInstance(this.getConf());
        job.setJobName(jobName);
        job.setInputFormatClass(DistCpUtils.getStrategy(this.getConf(), this.distCpContext));
        job.setJarByClass(DsgCopyMapper.class);
        this.configureOutputFormat(job);
        job.setMapperClass(DsgCopyMapper.class);
        job.setNumReduceTasks(0);
        job.setMapOutputKeyClass(Text.class);
        job.setMapOutputValueClass(Text.class);
        job.setOutputFormatClass(DsgCopyOutputFormat.class);
        job.getConfiguration().set("mapreduce.map.speculative", "false");
        job.getConfiguration().set("mapreduce.job.maps", String.valueOf(this.inputOptions.getMaxMaps()));
        job.getConfiguration().set("mapreduce.job.end-notification.url", callbackUrl);
        if(StringUtils.isNotEmpty(yarnQueue)){
            job.getConfiguration().set("mapred.job.queue.name", yarnQueue);
        }
      /*  if (this.inputOptions.getSslConfigurationFile() != null) {
            this.setupSSLConfig(job);
        }*/
        this.inputOptions.appendToConf(job.getConfiguration());
        return job;
    }

/*    private void setupSSLConfig(Job job) throws IOException {
        Configuration configuration = job.getConfiguration();
        Path sslConfigPath = new Path(configuration.getResource(this.inputOptions.getSslConfigurationFile()).toString());
        this.addSSLFilesToDistCache(job, sslConfigPath);
        configuration.set("distcp.keystore.resource", sslConfigPath.getName());
        configuration.set("dfs.https.client.keystore.resource", sslConfigPath.getName());
    }*/

 /*   private void addSSLFilesToDistCache(Job job, Path sslConfigPath) throws IOException {
        Configuration configuration = job.getConfiguration();
        FileSystem localFS = FileSystem.getLocal(configuration);
        Configuration sslConf = new Configuration(false);
        sslConf.addResource(sslConfigPath);
        Path localStorePath = this.getLocalStorePath(sslConf, "ssl.client.truststore.location");
        job.addCacheFile(localStorePath.makeQualified(localFS.getUri(), localFS.getWorkingDirectory()).toUri());
        configuration.set("ssl.client.truststore.location", localStorePath.getName());
        localStorePath = this.getLocalStorePath(sslConf, "ssl.client.keystore.location");
        job.addCacheFile(localStorePath.makeQualified(localFS.getUri(), localFS.getWorkingDirectory()).toUri());
        configuration.set("ssl.client.keystore.location", localStorePath.getName());
        job.addCacheFile(sslConfigPath.makeQualified(localFS.getUri(), localFS.getWorkingDirectory()).toUri());
    }*/

 /*   private Path getLocalStorePath(Configuration sslConf, String storeKey) throws IOException {
        if (sslConf.get(storeKey) != null) {
            return new Path(sslConf.get(storeKey));
        } else {
            throw new IOException("Store for " + storeKey + " is not set in " + this.inputOptions.getSslConfigurationFile());
        }
    }*/

    private void configureOutputFormat(Job job) throws IOException {
        Configuration configuration = job.getConfiguration();
        Path targetPath = this.inputOptions.getTargetPath();
        FileSystem targetFS = targetPath.getFileSystem(configuration);
        targetPath = targetPath.makeQualified(targetFS.getUri(), targetFS.getWorkingDirectory());
        if (this.inputOptions.shouldPreserve(DistCpOptions.FileAttribute.ACL)) {
            DistCpUtils.checkFileSystemAclSupport(targetFS);
        }

        if (this.inputOptions.shouldPreserve(DistCpOptions.FileAttribute.XATTR)) {
            DistCpUtils.checkFileSystemXAttrSupport(targetFS);
        }

        Path workDir;
        if (this.inputOptions.shouldAtomicCommit()) {
            workDir = this.inputOptions.getAtomicWorkPath();
            if (workDir == null) {
                workDir = targetPath.getParent();
            }

            workDir = new Path(workDir, "._WIP_" + targetPath.getName() + rand.nextInt());
            FileSystem workFS = workDir.getFileSystem(configuration);
            if (!DsgDistCpUtils2.compareFs(targetFS, workFS)) {
                throw new IllegalArgumentException("Work path " + workDir + " and target path " + targetPath + " are in different file system");
            }

            CopyOutputFormat.setWorkingDirectory(job, workDir);
        } else {
            CopyOutputFormat.setWorkingDirectory(job, targetPath);
        }

        CopyOutputFormat.setCommitDirectory(job, targetPath);
        workDir = this.inputOptions.getLogPath();
        if (workDir == null) {
            workDir = new Path(this.metaFolder, "_logs");
        } else {
            LOG.info("DistCp job log path: " + workDir);
        }

        CopyOutputFormat.setOutputPath(job, workDir);
    }

    protected Path createInputFileListing(Job job) throws IOException, URISyntaxException {
        Path fileListingPath = this.getFileListingPath();
        Configuration configuration = job.getConfiguration();
        CopyListing copyListing = CopyListing.getCopyListing(configuration, job.getCredentials(), this.distCpContext);
        copyListing.buildListing(fileListingPath, this.distCpContext);
        return fileListingPath;
    }

    protected Path getFileListingPath() throws IOException {
        String fileListPathStr = this.metaFolder + "/fileList.seq";
        Path path = new Path(fileListPathStr);
        return new Path(path.toUri().normalize().toString());
    }

    private Path createMetaFolderPath() throws Exception {
        Configuration configuration = this.getConf();
        Path stagingDir = JobSubmissionFiles.getStagingDir(new Cluster(configuration), configuration);
        Path metaFolderPath = new Path(stagingDir, "_distcp" + String.valueOf(rand.nextInt()));
        if (LOG.isDebugEnabled()) {
            LOG.debug("Meta folder location: " + metaFolderPath);
        }

        configuration.set("distcp.meta.folder", metaFolderPath.toString());
        return metaFolderPath;
    }

    private static Configuration getDefaultConf() {
        Configuration config = new Configuration();
        config.addResource("distcp-default.xml");
        return config;
    }

    private synchronized void cleanup() {
        try {
            if (this.metaFolder == null) {
                return;
            }

            this.jobFS.delete(this.metaFolder, true);
            this.metaFolder = null;
        } catch (IOException var2) {
            LOG.error("Unable to cleanup meta folder: " + this.metaFolder, var2);
        }

    }

    private boolean isSubmitted() {
        return this.submitted;
    }

    private static class Cleanup implements Runnable {
        private  HdfsDistCP distCp;

        public Cleanup(HdfsDistCP distCp) {
            this.distCp = distCp;
        }

        @Override
        public void run() {
            if (!this.distCp.isSubmitted()) {
                this.distCp.cleanup();
            }
        }
    }

    static class InvalidInputException extends RuntimeException {
        public InvalidInputException(String message) {
            super(message);
        }
    }

    static class DuplicateFileException extends RuntimeException {
        public DuplicateFileException(String message) {
            super(message);
        }
    }

    public String getYarnQueue() {
        return yarnQueue;
    }

    public void setYarnQueue(String yarnQueue) {
        this.yarnQueue = yarnQueue;
    }
}
