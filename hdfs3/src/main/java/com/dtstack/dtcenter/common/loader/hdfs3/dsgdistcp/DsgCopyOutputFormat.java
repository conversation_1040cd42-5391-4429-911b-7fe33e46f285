package com.dtstack.dtcenter.common.loader.hdfs3.dsgdistcp;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.mapreduce.Job;
import org.apache.hadoop.mapreduce.JobContext;
import org.apache.hadoop.mapreduce.OutputCommitter;
import org.apache.hadoop.mapreduce.TaskAttemptContext;
import org.apache.hadoop.mapreduce.lib.output.TextOutputFormat;
import org.apache.hadoop.mapreduce.security.TokenCache;

import java.io.IOException;

/**
 * @描述
 * @创建人 lf
 * @创建时间 2022/4/27
 * @create 2022-04-27-16:26
 */
public class DsgCopyOutputFormat<K, V> extends TextOutputFormat<K, V> {
    public DsgCopyOutputFormat() {
    }

    public static void setWorkingDirectory(Job job, Path workingDirectory) {
        job.getConfiguration().set("distcp.target.work.path", workingDirectory.toString());
    }

    public static void setCommitDirectory(Job job, Path commitDirectory) {
        job.getConfiguration().set("distcp.target.final.path", commitDirectory.toString());
    }

    public static Path getWorkingDirectory(Job job) {
        return getWorkingDirectory(job.getConfiguration());
    }

    private static Path getWorkingDirectory(Configuration conf) {
        String workingDirectory = conf.get("distcp.target.work.path");
        return workingDirectory != null && !workingDirectory.isEmpty() ? new Path(workingDirectory) : null;
    }

    public static Path getCommitDirectory(Job job) {
        return getCommitDirectory(job.getConfiguration());
    }

    private static Path getCommitDirectory(Configuration conf) {
        String commitDirectory = conf.get("distcp.target.final.path");
        return commitDirectory != null && !commitDirectory.isEmpty() ? new Path(commitDirectory) : null;
    }

    @Override
    public OutputCommitter getOutputCommitter(TaskAttemptContext context) throws IOException {
        return new DsgCopyCommitter(getOutputPath(context), context);
    }

    @Override
    public void checkOutputSpecs(JobContext context) throws IOException {
        Configuration conf = context.getConfiguration();
        if (getCommitDirectory(conf) == null) {
            throw new IllegalStateException("Commit directory not configured");
        } else {
            Path workingPath = getWorkingDirectory(conf);
            if (workingPath == null) {
                throw new IllegalStateException("Working directory not configured");
            } else {
                TokenCache.obtainTokensForNamenodes(context.getCredentials(), new Path[]{workingPath}, conf);
            }
        }
    }
}
