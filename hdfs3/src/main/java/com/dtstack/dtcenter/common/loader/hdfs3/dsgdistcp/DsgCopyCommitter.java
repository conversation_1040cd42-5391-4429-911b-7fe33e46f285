package com.dtstack.dtcenter.common.loader.hdfs3.dsgdistcp;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.io.IOUtils;
import org.apache.hadoop.io.SequenceFile;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.JobContext;
import org.apache.hadoop.mapreduce.JobStatus;
import org.apache.hadoop.mapreduce.TaskAttemptContext;
import org.apache.hadoop.mapreduce.lib.output.FileOutputCommitter;
import org.apache.hadoop.security.Credentials;
import org.apache.hadoop.tools.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;

/**
 * @描述
 * @创建人 lf
 * @创建时间 2022/4/27
 * @create 2022-04-27-16:27
 */
public class DsgCopyCommitter extends FileOutputCommitter {
    private static final Log LOG = LogFactory.getLog(DsgCopyCommitter.class);
    private final TaskAttemptContext taskAttemptContext;
    private boolean syncFolder = false;
    private boolean overwrite = false;
    private boolean targetPathExists = true;

    public DsgCopyCommitter(Path outputPath, TaskAttemptContext context) throws IOException {
        super(outputPath, context);
        this.taskAttemptContext = context;
    }

    public void commitJob(JobContext jobContext) throws IOException {
        Configuration conf = jobContext.getConfiguration();
        this.syncFolder = conf.getBoolean("distcp.sync.folders", false);
        this.overwrite = conf.getBoolean("distcp.copy.overwrite", false);
        this.targetPathExists = conf.getBoolean("distcp.target.path.exists", true);
        super.commitJob(jobContext);
        this.cleanupTempFiles(jobContext);
        String attributes = conf.get("distcp.preserve.status");
        if (attributes != null && !attributes.isEmpty()) {
            this.preserveFileAttributesForDirectories(conf);
        }

        try {
            if (conf.getBoolean("distcp.delete.missing.source", false)) {
                this.deleteMissing(conf);
            } else if (conf.getBoolean("distcp.atomic.copy", false)) {
                this.commitData(conf);
            }

            this.taskAttemptContext.setStatus("Commit Successful");
        } finally {
            this.cleanup(conf);
        }

    }

    public void abortJob(JobContext jobContext, JobStatus.State state) throws IOException {
        try {
            super.abortJob(jobContext, state);
        } finally {
            this.cleanupTempFiles(jobContext);
            this.cleanup(jobContext.getConfiguration());
        }

    }

    private void cleanupTempFiles(JobContext context) {
        try {
            Configuration conf = context.getConfiguration();
            if(conf.getBoolean(DsgDistCpOptionSwitch.DIRECT_WRITE.getConfigLabel(),false)){
                System.out.println("direct is true,the .distcp.tmp file is  not exist");
                return;
            }
            Path targetWorkPath = new Path(conf.get("distcp.target.work.path"));
            FileSystem targetFS = targetWorkPath.getFileSystem(conf);
            String jobId = context.getJobID().toString();
            this.deleteAttemptTempFiles(targetWorkPath, targetFS, jobId);
            this.deleteAttemptTempFiles(targetWorkPath.getParent(), targetFS, jobId);
        } catch (Throwable var6) {
            LOG.warn("Unable to cleanup temp files", var6);
        }

    }

    private void deleteAttemptTempFiles(Path targetWorkPath, FileSystem targetFS, String jobId) throws IOException {
        FileStatus[] tempFiles = targetFS.globStatus(new Path(targetWorkPath, ".distcp.tmp." + jobId.replaceAll("job", "attempt") + "*"));
        if (tempFiles != null && tempFiles.length > 0) {
            FileStatus[] arr$ = tempFiles;
            int len$ = tempFiles.length;

            for(int i$ = 0; i$ < len$; ++i$) {
                FileStatus file = arr$[i$];
                LOG.info("Cleaning up " + file.getPath());
                targetFS.delete(file.getPath(), false);
            }
        }

    }

    private void cleanup(Configuration conf) {
        Path metaFolder = new Path(conf.get("distcp.meta.folder"));

        try {
            FileSystem fs = metaFolder.getFileSystem(conf);
            LOG.info("Cleaning up temporary work folder: " + metaFolder);
            fs.delete(metaFolder, true);
        } catch (IOException var4) {
            LOG.error("Exception encountered ", var4);
        }

    }

    private void preserveFileAttributesForDirectories(Configuration conf) throws IOException {
        String attrSymbols = conf.get("distcp.preserve.status");
        boolean syncOrOverwrite = this.syncFolder || this.overwrite;
        LOG.info("About to preserve attributes: " + attrSymbols);
        EnumSet<DsgDistCpOptions.FileAttribute> attributes = DsgDistCpUtils2.unpackAttributes(attrSymbols);
        Path sourceListing = new Path(conf.get("distcp.listing.file.path"));
        FileSystem clusterFS = sourceListing.getFileSystem(conf);
        SequenceFile.Reader sourceReader = new SequenceFile.Reader(conf, new SequenceFile.Reader.Option[]{SequenceFile.Reader.file(sourceListing)});
        long totalLen = clusterFS.getFileStatus(sourceListing).getLen();
        Path targetRoot = new Path(conf.get("distcp.target.work.path"));
        long preservedEntries = 0L;

        try {
            CopyListingFileStatus srcFileStatus = new CopyListingFileStatus();
            Text srcRelPath = new Text();

            label72:
            while(true) {
                Path targetFile;
                do {
                    do {
                        if (!sourceReader.next(srcRelPath, srcFileStatus)) {
                            break label72;
                        }
                    } while(!srcFileStatus.isDirectory());

                    targetFile = new Path(targetRoot.toString() + "/" + srcRelPath);
                } while(targetRoot.equals(targetFile) && syncOrOverwrite);

                FileSystem targetFS = targetFile.getFileSystem(conf);
                DsgDistCpUtils2.preserve(targetFS, targetFile, srcFileStatus, attributes);
                this.taskAttemptContext.progress();
                this.taskAttemptContext.setStatus("Preserving status on directory entries. [" + sourceReader.getPosition() * 100L / totalLen + "%]");
            }
        } finally {
            IOUtils.closeStream(sourceReader);
        }

        LOG.info("Preserved status on " + preservedEntries + " dir entries on target");
    }

    private void deleteMissing(Configuration conf) throws IOException {
        LOG.info("-delete option is enabled. About to remove entries from target that are missing in source");
        Path sourceListing = new Path(conf.get("distcp.listing.file.path"));
        FileSystem clusterFS = sourceListing.getFileSystem(conf);
        Path sortedSourceListing = DsgDistCpUtils2.sortListing(clusterFS, conf, sourceListing);
        Path targetListing = new Path(sourceListing.getParent(), "targetListing.seq");
        CopyListing target = new GlobbedCopyListing(new Configuration(conf), (Credentials)null);
        List<Path> targets = new ArrayList(1);
        Path targetFinalPath = new Path(conf.get("distcp.target.final.path"));
        targets.add(targetFinalPath);
        DistCpOptions.Builder builder = new DistCpOptions.Builder(targets, new Path("/NONE"));
        DistCpOptions.Builder overwrite = builder.withOverwrite(this.overwrite);
        DistCpOptions.Builder syncFolder = overwrite.withSyncFolder(this.syncFolder);
        DistCpOptions.Builder append = syncFolder.withAppend(true);
        DistCpOptions.Builder dynamic = append.withCopyStrategy("dynamic");
        DistCpOptions build = dynamic.build();
        DistCpContext distCpContext = new DistCpContext(build);
        distCpContext.setTargetPathExists(this.targetPathExists);
        target.buildListing(targetListing, distCpContext);
        Path sortedTargetListing = DsgDistCpUtils2.sortListing(clusterFS, conf, targetListing);
        long totalLen = clusterFS.getFileStatus(sortedTargetListing).getLen();
        SequenceFile.Reader sourceReader = new SequenceFile.Reader(conf, new SequenceFile.Reader.Option[]{SequenceFile.Reader.file(sortedSourceListing)});
        SequenceFile.Reader targetReader = new SequenceFile.Reader(conf, new SequenceFile.Reader.Option[]{SequenceFile.Reader.file(sortedTargetListing)});
        long deletedEntries = 0L;

        try {
            CopyListingFileStatus srcFileStatus = new CopyListingFileStatus();
            Text srcRelPath = new Text();
            CopyListingFileStatus trgtFileStatus = new CopyListingFileStatus();
            Text trgtRelPath = new Text();
            FileSystem targetFS = targetFinalPath.getFileSystem(conf);
            boolean srcAvailable = sourceReader.next(srcRelPath, srcFileStatus);

            while(targetReader.next(trgtRelPath, trgtFileStatus)) {
                while(srcAvailable && trgtRelPath.compareTo(srcRelPath) > 0) {
                    srcAvailable = sourceReader.next(srcRelPath, srcFileStatus);
                }

                if (!srcAvailable || !trgtRelPath.equals(srcRelPath)) {
                    boolean result = !targetFS.exists(trgtFileStatus.getPath()) || targetFS.delete(trgtFileStatus.getPath(), true);
                    if (!result) {
                        throw new IOException("Unable to delete " + trgtFileStatus.getPath());
                    }

                    LOG.info("Deleted " + trgtFileStatus.getPath() + " - Missing at source");
                    ++deletedEntries;
                    this.taskAttemptContext.progress();
                    this.taskAttemptContext.setStatus("Deleting missing files from target. [" + targetReader.getPosition() * 100L / totalLen + "%]");
                }
            }
        } finally {
            IOUtils.closeStream(sourceReader);
            IOUtils.closeStream(targetReader);
        }

        LOG.info("Deleted " + deletedEntries + " from target: " + targets.get(0));
    }

    private void commitData(Configuration conf) throws IOException {
        Path workDir = new Path(conf.get("distcp.target.work.path"));
        Path finalDir = new Path(conf.get("distcp.target.final.path"));
        FileSystem targetFS = workDir.getFileSystem(conf);
        LOG.info("Atomic commit enabled. Moving " + workDir + " to " + finalDir);
        if (targetFS.exists(finalDir) && targetFS.exists(workDir)) {
            LOG.error("Pre-existing final-path found at: " + finalDir);
            throw new IOException("Target-path can't be committed to because it exists at " + finalDir + ". Copied data is in temp-dir: " + workDir + ". ");
        } else {
            boolean result = targetFS.rename(workDir, finalDir);
            if (!result) {
                LOG.warn("Rename failed. Perhaps data already moved. Verifying...");
                result = targetFS.exists(finalDir) && !targetFS.exists(workDir);
            }

            if (result) {
                LOG.info("Data committed successfully to " + finalDir);
                this.taskAttemptContext.setStatus("Data committed successfully to " + finalDir);
            } else {
                LOG.error("Unable to commit data to " + finalDir);
                throw new IOException("Atomic commit failed. Temporary data in " + workDir + ", Unable to move to " + finalDir);
            }
        }
    }

}
