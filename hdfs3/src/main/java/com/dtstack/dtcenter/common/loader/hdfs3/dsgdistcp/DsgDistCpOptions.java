package com.dtstack.dtcenter.common.loader.hdfs3.dsgdistcp;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;

import java.util.EnumSet;
import java.util.Iterator;
import java.util.List;
import java.util.NoSuchElementException;

/**
 * @描述
 * @创建人 lf
 * @创建时间 2022/4/27
 * @create 2022-04-27-17:14
 */
public class DsgDistCpOptions {
    private boolean directWrite = false;


    public boolean isDirectWrite() {
        return directWrite;
    }

    public void setDirectWrite(boolean directWrite) {
        this.directWrite = directWrite;
    }

    private boolean atomicCommit = false;
    private boolean syncFolder = false;
    private boolean deleteMissing = false;
    private boolean ignoreFailures = false;
    private boolean overwrite = false;
    private boolean append = false;
    private boolean skipCRC = false;
    private boolean blocking = true;
    private int maxMaps = 20;
    private int mapBandwidth = 100;
    private String sslConfigurationFile;
    private String copyStrategy = "uniformsize";
    private EnumSet<FileAttribute> preserveStatus = EnumSet.noneOf(DsgDistCpOptions.FileAttribute.class);
    private Path atomicWorkPath;
    private Path logPath;
    private Path sourceFileListing;
    private List<Path> sourcePaths;
    private Path targetPath;
    private boolean targetPathExists = true;

    public DsgDistCpOptions(List<Path> sourcePaths, Path targetPath) {
        assert sourcePaths != null && !sourcePaths.isEmpty() : "Invalid source paths";

        assert targetPath != null : "Invalid Target path";

        this.sourcePaths = sourcePaths;
        this.targetPath = targetPath;
    }

    public DsgDistCpOptions(Path sourceFileListing, Path targetPath) {
        assert sourceFileListing != null : "Invalid source paths";

        assert targetPath != null : "Invalid Target path";

        this.sourceFileListing = sourceFileListing;
        this.targetPath = targetPath;
    }

    public DsgDistCpOptions(DsgDistCpOptions that) {

        if (this != that && that != null) {
            this.atomicCommit = that.atomicCommit;
            this.syncFolder = that.syncFolder;
            this.deleteMissing = that.deleteMissing;
            this.ignoreFailures = that.ignoreFailures;
            this.overwrite = that.overwrite;
            this.skipCRC = that.skipCRC;
            this.blocking = that.blocking;
            this.maxMaps = that.maxMaps;
            this.mapBandwidth = that.mapBandwidth;
            this.sslConfigurationFile = that.getSslConfigurationFile();
            this.copyStrategy = that.copyStrategy;
            this.preserveStatus = that.preserveStatus;
            this.atomicWorkPath = that.getAtomicWorkPath();
            this.logPath = that.getLogPath();
            this.sourceFileListing = that.getSourceFileListing();
            this.sourcePaths = that.getSourcePaths();
            this.targetPath = that.getTargetPath();
            this.targetPathExists = that.getTargetPathExists();
        }

    }

    public boolean shouldAtomicCommit() {
        return this.atomicCommit;
    }

    public void setAtomicCommit(boolean atomicCommit) {
        this.validateByDsg(DsgDistCpOptionSwitch.ATOMIC_COMMIT, atomicCommit);
        this.atomicCommit = atomicCommit;
    }

    public boolean shouldSyncFolder() {
        return this.syncFolder;
    }

    public void setSyncFolder(boolean syncFolder) {
        this.validateByDsg(DsgDistCpOptionSwitch.SYNC_FOLDERS, syncFolder);
        this.syncFolder = syncFolder;
    }

    public boolean shouldDeleteMissing() {
        return this.deleteMissing;
    }

    public void setDeleteMissing(boolean deleteMissing) {
        this.validateByDsg(DsgDistCpOptionSwitch.DELETE_MISSING, deleteMissing);
        this.deleteMissing = deleteMissing;
    }

    public boolean shouldIgnoreFailures() {
        return this.ignoreFailures;
    }

    public void setIgnoreFailures(boolean ignoreFailures) {
        this.ignoreFailures = ignoreFailures;
    }

    public boolean shouldBlock() {
        return this.blocking;
    }

    public void setBlocking(boolean blocking) {
        this.blocking = blocking;
    }

    public boolean shouldOverwrite() {
        return this.overwrite;
    }

    public void setOverwrite(boolean overwrite) {
        this.validateByDsg(DsgDistCpOptionSwitch.OVERWRITE, overwrite);
        this.overwrite = overwrite;
    }

    public boolean shouldAppend() {
        return this.append;
    }

    public void setAppend(boolean append) {
        this.validateByDsg(DsgDistCpOptionSwitch.APPEND, append);
        this.append = append;
    }

    public boolean shouldSkipCRC() {
        return this.skipCRC;
    }

    public void setSkipCRC(boolean skipCRC) {
        this.validateByDsg(DsgDistCpOptionSwitch.SKIP_CRC, skipCRC);
        this.skipCRC = skipCRC;
    }

    public int getMaxMaps() {
        return this.maxMaps;
    }

    public void setMaxMaps(int maxMaps) {
        this.maxMaps = Math.max(maxMaps, 1);
    }

    public int getMapBandwidth() {
        return this.mapBandwidth;
    }

    public void setMapBandwidth(int mapBandwidth) {
        assert mapBandwidth > 0 : "Bandwidth " + mapBandwidth + " is invalid (should be > 0)";

        this.mapBandwidth = mapBandwidth;
    }

    public String getSslConfigurationFile() {
        return this.sslConfigurationFile;
    }

    public void setSslConfigurationFile(String sslConfigurationFile) {
        this.sslConfigurationFile = sslConfigurationFile;
    }

   /* public Iterator<DsgDistCpOptions.FileAttribute> preserveAttributes() {
        return this.preserveStatus.iterator();
    }*/

    public boolean shouldPreserve(DsgDistCpOptions.FileAttribute attribute) {
        return this.preserveStatus.contains(attribute);
    }

    public void preserve(DsgDistCpOptions.FileAttribute fileAttribute) {
        Iterator i$ = this.preserveStatus.iterator();

        DsgDistCpOptions.FileAttribute attribute;
        do {
            if (!i$.hasNext()) {
                this.preserveStatus.add(fileAttribute);
                return;
            }

            attribute = (DsgDistCpOptions.FileAttribute)i$.next();
        } while(!attribute.equals(fileAttribute));

    }

    public Path getAtomicWorkPath() {
        return this.atomicWorkPath;
    }

    public void setAtomicWorkPath(Path atomicWorkPath) {
        this.atomicWorkPath = atomicWorkPath;
    }

    public Path getLogPath() {
        return this.logPath;
    }

    public void setLogPath(Path logPath) {
        this.logPath = logPath;
    }

    public String getCopyStrategy() {
        return this.copyStrategy;
    }

    public void setCopyStrategy(String copyStrategy) {
        this.copyStrategy = copyStrategy;
    }

    public Path getSourceFileListing() {
        return this.sourceFileListing;
    }

    public List<Path> getSourcePaths() {
        return this.sourcePaths;
    }

    public void setSourcePaths(List<Path> sourcePaths) {
        assert sourcePaths != null && sourcePaths.size() != 0;

        this.sourcePaths = sourcePaths;
    }

    public Path getTargetPath() {
        return this.targetPath;
    }

    public boolean getTargetPathExists() {
        return this.targetPathExists;
    }

    public boolean setTargetPathExists(boolean targetPathExists) {
        return this.targetPathExists = targetPathExists;
    }

    public void validateByDsg(DsgDistCpOptionSwitch option, boolean value) {
        boolean syncFolder = option == DsgDistCpOptionSwitch.SYNC_FOLDERS ? value : this.syncFolder;
        boolean overwrite = option == DsgDistCpOptionSwitch.OVERWRITE ? value : this.overwrite;
        boolean deleteMissing = option == DsgDistCpOptionSwitch.DELETE_MISSING ? value : this.deleteMissing;
        boolean atomicCommit = option == DsgDistCpOptionSwitch.ATOMIC_COMMIT ? value : this.atomicCommit;
        boolean skipCRC = option == DsgDistCpOptionSwitch.SKIP_CRC ? value : this.skipCRC;
        boolean append = option == DsgDistCpOptionSwitch.APPEND ? value : this.append;
        boolean directWrite = option == DsgDistCpOptionSwitch.DIRECT_WRITE ? value : this.directWrite;

        if (syncFolder && atomicCommit) {
            throw new IllegalArgumentException("Atomic commit can't be used with sync folder or overwrite options");
        } else if (deleteMissing && !overwrite && !syncFolder) {
            throw new IllegalArgumentException("Delete missing is applicable only with update or overwrite options");
        } else if (overwrite && syncFolder) {
            throw new IllegalArgumentException("Overwrite and update options are mutually exclusive");
        } else if (!syncFolder && skipCRC) {
            throw new IllegalArgumentException("Skip CRC is valid only with update options");
        } else if (!syncFolder && append) {
            throw new IllegalArgumentException("Append is valid only with update options");
        } else if (skipCRC && append) {
            throw new IllegalArgumentException("Append is disallowed when skipping CRC");
        }
    }

    public void appendToConf(Configuration conf) {
        DsgDistCpOptionSwitch.addToConf(conf, DsgDistCpOptionSwitch.ATOMIC_COMMIT, String.valueOf(this.atomicCommit));
        DsgDistCpOptionSwitch.addToConf(conf, DsgDistCpOptionSwitch.IGNORE_FAILURES, String.valueOf(this.ignoreFailures));
        DsgDistCpOptionSwitch.addToConf(conf, DsgDistCpOptionSwitch.SYNC_FOLDERS, String.valueOf(this.syncFolder));
        DsgDistCpOptionSwitch.addToConf(conf, DsgDistCpOptionSwitch.DELETE_MISSING, String.valueOf(this.deleteMissing));
        DsgDistCpOptionSwitch.addToConf(conf, DsgDistCpOptionSwitch.OVERWRITE, String.valueOf(this.overwrite));
        DsgDistCpOptionSwitch.addToConf(conf, DsgDistCpOptionSwitch.APPEND, String.valueOf(this.append));
        DsgDistCpOptionSwitch.addToConf(conf, DsgDistCpOptionSwitch.SKIP_CRC, String.valueOf(this.skipCRC));
        DsgDistCpOptionSwitch.addToConf(conf, DsgDistCpOptionSwitch.BANDWIDTH, String.valueOf(this.mapBandwidth));
        DsgDistCpOptionSwitch.addToConf(conf, DsgDistCpOptionSwitch.DIRECT_WRITE, String.valueOf(this.directWrite));
        DsgDistCpOptionSwitch.addToConf(conf, DsgDistCpOptionSwitch.PRESERVE_STATUS, DsgDistCpUtils2.packAttributes(this.preserveStatus));
    }

    public String toString() {
        return "DistCpOptions{atomicCommit=" + this.atomicCommit + ", syncFolder=" + this.syncFolder + ", deleteMissing=" + this.deleteMissing + ", ignoreFailures=" + this.ignoreFailures + ", maxMaps=" + this.maxMaps + ", sslConfigurationFile='" + this.sslConfigurationFile + '\'' + ", copyStrategy='" + this.copyStrategy + '\'' + ", sourceFileListing=" + this.sourceFileListing + ", sourcePaths=" + this.sourcePaths + ", targetPath=" + this.targetPath + ", targetPathExists=" + this.targetPathExists + '}';
    }

    protected DsgDistCpOptions clone() throws CloneNotSupportedException {
        return (DsgDistCpOptions)super.clone();
    }

    public static enum FileAttribute {
        REPLICATION,
        BLOCKSIZE,
        USER,
        GROUP,
        PERMISSION,
        CHECKSUMTYPE,
        ACL,
        XATTR;

        private FileAttribute() {
        }

        public static DsgDistCpOptions.FileAttribute getAttribute(char symbol) {
            DsgDistCpOptions.FileAttribute[] arr$ = values();
            int len$ = arr$.length;

            for(int i$ = 0; i$ < len$; ++i$) {
                DsgDistCpOptions.FileAttribute attribute = arr$[i$];
                if (attribute.name().charAt(0) == Character.toUpperCase(symbol)) {
                    return attribute;
                }
            }

            throw new NoSuchElementException("No attribute for " + symbol);
        }
    }
}


