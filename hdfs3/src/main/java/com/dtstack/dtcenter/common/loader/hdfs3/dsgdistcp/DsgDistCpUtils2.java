package com.dtstack.dtcenter.common.loader.hdfs3.dsgdistcp;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileChecksum;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.fs.permission.AclEntry;
import org.apache.hadoop.fs.permission.AclUtil;
import org.apache.hadoop.fs.permission.FsPermission;
import org.apache.hadoop.io.SequenceFile;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.InputFormat;
import org.apache.hadoop.tools.CopyListing;
import org.apache.hadoop.tools.CopyListingFileStatus;
import org.apache.hadoop.tools.mapred.UniformSizeInputFormat;

import java.io.IOException;
import java.net.InetAddress;
import java.net.URI;
import java.net.UnknownHostException;
import java.text.DecimalFormat;
import java.util.*;

/**
 * @描述
 * @创建人 lf
 * @创建时间 2022/4/28
 * @create 2022-04-28-13:49
 */
public class DsgDistCpUtils2 {
    private static final Log LOG = LogFactory.getLog(DsgDistCpUtils2.class);
    private static ThreadLocal<DecimalFormat> FORMATTER = new ThreadLocal<DecimalFormat>() {
        protected DecimalFormat initialValue() {
            return new DecimalFormat("0.0");
        }
    };

    public DsgDistCpUtils2() {
    }

    public static long getFileSize(Path path, Configuration configuration) throws IOException {
        if (LOG.isDebugEnabled()) {
            LOG.debug("Retrieving file size for: " + path);
        }

        return path.getFileSystem(configuration).getFileStatus(path).getLen();
    }

    public static <T> void publish(Configuration configuration, String label, T value) {
        configuration.set(label, String.valueOf(value));
    }

    public static int getInt(Configuration configuration, String label) {
        int value = configuration.getInt(label, -1);

        assert value >= 0 : "Couldn't find " + label;

        return value;
    }

    public static long getLong(Configuration configuration, String label) {
        long value = configuration.getLong(label, -1L);

        assert value >= 0L : "Couldn't find " + label;

        return value;
    }

    public static Class<? extends InputFormat> getStrategy(Configuration conf, DsgDistCpOptions options) {
        String confLabel = "distcp." + options.getCopyStrategy().toLowerCase(Locale.getDefault()) + ".strategy.impl";
        return conf.getClass(confLabel, UniformSizeInputFormat.class, InputFormat.class);
    }

    public static String getRelativePath(Path sourceRootPath, Path childPath) {
        String childPathString = childPath.toUri().getPath();
        String sourceRootPathString = sourceRootPath.toUri().getPath();
        return sourceRootPathString.equals("/") ? childPathString : childPathString.substring(sourceRootPathString.length());
    }

    public static String packAttributes(EnumSet<DsgDistCpOptions.FileAttribute> attributes) {
        StringBuffer buffer = new StringBuffer(5);
        int len = 0;

        for(Iterator i$ = attributes.iterator(); i$.hasNext(); ++len) {
            DsgDistCpOptions.FileAttribute attribute = (DsgDistCpOptions.FileAttribute)i$.next();
            buffer.append(attribute.name().charAt(0));
        }

        return buffer.substring(0, len);
    }

    public static EnumSet<DsgDistCpOptions.FileAttribute> unpackAttributes(String attributes) {
        EnumSet<DsgDistCpOptions.FileAttribute> retValue = EnumSet.noneOf(DsgDistCpOptions.FileAttribute.class);
        if (attributes != null) {
            for(int index = 0; index < attributes.length(); ++index) {
                retValue.add(DsgDistCpOptions.FileAttribute.getAttribute(attributes.charAt(index)));
            }
        }

        return retValue;
    }

    public static void preserve(FileSystem targetFS, Path path, CopyListingFileStatus srcFileStatus, EnumSet<DsgDistCpOptions.FileAttribute> attributes) throws IOException {
        FileStatus targetFileStatus = targetFS.getFileStatus(path);
        String group = targetFileStatus.getGroup();
        String user = targetFileStatus.getOwner();
        boolean chown = false;
        if (attributes.contains(DsgDistCpOptions.FileAttribute.ACL)) {
            List<AclEntry> srcAcl = srcFileStatus.getAclEntries();
            List<AclEntry> targetAcl = getAcl(targetFS, targetFileStatus);
            if (!srcAcl.equals(targetAcl)) {
                targetFS.setAcl(path, srcAcl);
            }

            if (srcFileStatus.getPermission().getStickyBit() != targetFileStatus.getPermission().getStickyBit()) {
                targetFS.setPermission(path, srcFileStatus.getPermission());
            }
        } else if (attributes.contains(DsgDistCpOptions.FileAttribute.PERMISSION) && !srcFileStatus.getPermission().equals(targetFileStatus.getPermission())) {
            targetFS.setPermission(path, srcFileStatus.getPermission());
        }

        if (attributes.contains(DsgDistCpOptions.FileAttribute.XATTR)) {
            Map<String, byte[]> srcXAttrs = srcFileStatus.getXAttrs();
            Map<String, byte[]> targetXAttrs = getXAttrs(targetFS, path);
            if (!srcXAttrs.equals(targetXAttrs)) {
                Iterator iter = srcXAttrs.entrySet().iterator();

                while(iter.hasNext()) {
                    Map.Entry<String, byte[]> entry = (Map.Entry)iter.next();
                    targetFS.setXAttr(path, (String)entry.getKey(), (byte[])entry.getValue());
                }
            }
        }

        if (attributes.contains(DsgDistCpOptions.FileAttribute.REPLICATION) && !targetFileStatus.isDirectory() && srcFileStatus.getReplication() != targetFileStatus.getReplication()) {
            targetFS.setReplication(path, srcFileStatus.getReplication());
        }

        if (attributes.contains(DsgDistCpOptions.FileAttribute.GROUP) && !group.equals(srcFileStatus.getGroup())) {
            group = srcFileStatus.getGroup();
            chown = true;
        }

        if (attributes.contains(DsgDistCpOptions.FileAttribute.USER) && !user.equals(srcFileStatus.getOwner())) {
            user = srcFileStatus.getOwner();
            chown = true;
        }

        if (chown) {
            targetFS.setOwner(path, user, group);
        }

    }

    public static List<AclEntry> getAcl(FileSystem fileSystem, FileStatus fileStatus) throws IOException {
        List<AclEntry> entries = fileSystem.getAclStatus(fileStatus.getPath()).getEntries();
        return AclUtil.getAclFromPermAndEntries(fileStatus.getPermission(), entries);
    }

    public static Map<String, byte[]> getXAttrs(FileSystem fileSystem, Path path) throws IOException {
        return fileSystem.getXAttrs(path);
    }

    public static CopyListingFileStatus toCopyListingFileStatus(FileSystem fileSystem, FileStatus fileStatus, boolean preserveAcls, boolean preserveXAttrs) throws IOException {
        CopyListingFileStatus copyListingFileStatus = new CopyListingFileStatus(fileStatus);
        if (preserveAcls) {
            FsPermission perm = fileStatus.getPermission();
            if (perm.getAclBit()) {
                List<AclEntry> aclEntries = fileSystem.getAclStatus(fileStatus.getPath()).getEntries();
                copyListingFileStatus.setAclEntries(aclEntries);
            }
        }

        if (preserveXAttrs) {
            Map<String, byte[]> xAttrs = fileSystem.getXAttrs(fileStatus.getPath());
            copyListingFileStatus.setXAttrs(xAttrs);
        }

        return copyListingFileStatus;
    }

    public static Path sortListing(FileSystem fs, Configuration conf, Path sourceListing) throws IOException {
        SequenceFile.Sorter sorter = new SequenceFile.Sorter(fs, Text.class, CopyListingFileStatus.class, conf);
        Path output = new Path(sourceListing.toString() + "_sorted");
        if (fs.exists(output)) {
            fs.delete(output, false);
        }

        sorter.sort(sourceListing, output);
        return output;
    }

    public static void checkFileSystemAclSupport(FileSystem fs) throws CopyListing.AclsNotSupportedException {
        try {
            fs.getAclStatus(new Path("/"));
        } catch (Exception var2) {
            throw new CopyListing.AclsNotSupportedException("ACLs not supported for file system: " + fs.getUri());
        }
    }

    public static void checkFileSystemXAttrSupport(FileSystem fs) throws CopyListing.XAttrsNotSupportedException {
        try {
            fs.getXAttrs(new Path("/"));
        } catch (Exception var2) {
            throw new CopyListing.XAttrsNotSupportedException("XAttrs not supported for file system: " + fs.getUri());
        }
    }

    public static DecimalFormat getFormatter() {
        return (DecimalFormat)FORMATTER.get();
    }

    public static String getStringDescriptionFor(long nBytes) {
        char[] units = new char[]{'B', 'K', 'M', 'G', 'T', 'P'};
        double current = (double)nBytes;
        double prev = current;

        int index;
        for(index = 0; (current /= 1024.0D) >= 1.0D; ++index) {
            prev = current;
        }

        assert index < units.length : "Too large a number.";

        return getFormatter().format(prev) + units[index];
    }

    public static boolean checksumsAreEqual(FileSystem sourceFS, Path source, FileChecksum sourceChecksum, FileSystem targetFS, Path target) throws IOException {
        FileChecksum targetChecksum = null;

        try {
            sourceChecksum = sourceChecksum != null ? sourceChecksum : sourceFS.getFileChecksum(source);
            targetChecksum = targetFS.getFileChecksum(target);
        } catch (IOException var7) {
            LOG.error("Unable to retrieve checksum for " + source + " or " + target, var7);
        }

        return sourceChecksum == null || targetChecksum == null || sourceChecksum.equals(targetChecksum);
    }

    public static boolean compareFs(FileSystem srcFs, FileSystem destFs) {
        URI srcUri = srcFs.getUri();
        URI dstUri = destFs.getUri();
        if (srcUri.getScheme() == null) {
            return false;
        } else if (!srcUri.getScheme().equals(dstUri.getScheme())) {
            return false;
        } else {
            String srcHost = srcUri.getHost();
            String dstHost = dstUri.getHost();
            if (srcHost != null && dstHost != null) {
                try {
                    srcHost = InetAddress.getByName(srcHost).getCanonicalHostName();
                    dstHost = InetAddress.getByName(dstHost).getCanonicalHostName();
                } catch (UnknownHostException var7) {
                    if (LOG.isDebugEnabled()) {
                        LOG.debug("Could not compare file-systems. Unknown host: ", var7);
                    }

                    return false;
                }

                if (!srcHost.equals(dstHost)) {
                    return false;
                }
            } else {
                if (srcHost == null && dstHost != null) {
                    return false;
                }

                if (srcHost != null) {
                    return false;
                }
            }

            return srcUri.getPort() == dstUri.getPort();
        }
    }
}
