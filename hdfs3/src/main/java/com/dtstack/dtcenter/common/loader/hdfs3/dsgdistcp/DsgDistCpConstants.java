package com.dtstack.dtcenter.common.loader.hdfs3.dsgdistcp;

/**
 * @描述
 * @创建人 lf
 * @创建时间 2022/4/27
 * @create 2022-04-27-15:30
 */
public final class DsgDistCpConstants {
    public static final int DEFAULT_MAPS = 20;
    public static final int DEFAULT_BANDWIDTH_MB = 100;
    public static final String UNIFORMSIZE = "uniformsize";
    public static final String CONF_LABEL_ATOMIC_COPY = "distcp.atomic.copy";
    public static final String CONF_LABEL_WORK_PATH = "distcp.work.path";
    public static final String CONF_LABEL_LOG_PATH = "distcp.log.path";
    public static final String CONF_LABEL_IGNORE_FAILURES = "distcp.ignore.failures";
    public static final String CONF_LABEL_PRESERVE_STATUS = "distcp.preserve.status";
    public static final String CONF_LABEL_SYNC_FOLDERS = "distcp.sync.folders";
    public static final String CONF_LABEL_DELETE_MISSING = "distcp.delete.missing.source";
    public static final String CONF_LABEL_SSL_CONF = "distcp.keystore.resource";
    public static final String CONF_LABEL_MAX_MAPS = "distcp.max.maps";
    public static final String CONF_LABEL_SOURCE_LISTING = "distcp.source.listing";
    public static final String CONF_LABEL_COPY_STRATEGY = "distcp.copy.strategy";
    public static final String CONF_LABEL_SKIP_CRC = "distcp.skip.crc";
    public static final String CONF_LABEL_OVERWRITE = "distcp.copy.overwrite";
    public static final String CONF_LABEL_APPEND = "distcp.copy.append";
    public static final String CONF_LABEL_BANDWIDTH_MB = "distcp.map.bandwidth.mb";
    public static final String CONF_LABEL_MAX_CHUNKS_TOLERABLE = "distcp.dynamic.max.chunks.tolerable";
    public static final String CONF_LABEL_MAX_CHUNKS_IDEAL = "distcp.dynamic.max.chunks.ideal";
    public static final String CONF_LABEL_MIN_RECORDS_PER_CHUNK = "distcp.dynamic.min.records_per_chunk";
    public static final String CONF_LABEL_SPLIT_RATIO = "distcp.dynamic.split.ratio";
    public static final String CONF_LABEL_TOTAL_BYTES_TO_BE_COPIED = "mapred.total.bytes.expected";
    public static final String CONF_LABEL_TOTAL_NUMBER_OF_RECORDS = "mapred.number.of.records";
    public static final String CONF_LABEL_SSL_KEYSTORE = "dfs.https.client.keystore.resource";
    public static final String CONF_LABEL_LISTING_FILE_PATH = "distcp.listing.file.path";
    public static final String CONF_LABEL_TARGET_WORK_PATH = "distcp.target.work.path";
    public static final String CONF_LABEL_TARGET_FINAL_PATH = "distcp.target.final.path";
    public static final String CONF_LABEL_TARGET_PATH_EXISTS = "distcp.target.path.exists";
    public static final String CONF_LABEL_DISTCP_JOB_ID = "distcp.job.id";
    public static final String CONF_LABEL_META_FOLDER = "distcp.meta.folder";
    public static final String CONF_LABEL_COPY_LISTING_CLASS = "distcp.copy.listing.class";
    public static final String CONF_LABEL_SSL_TRUST_STORE_LOCATION = "ssl.client.truststore.location";
    public static final String CONF_LABEL_SSL_KEY_STORE_LOCATION = "ssl.client.keystore.location";
    public static final String CONF_LABEL_DIRECT_WRITE = "distcp.direct.write";
    public static final int SUCCESS = 0;
    public static final int INVALID_ARGUMENT = -1;
    public static final int DUPLICATE_INPUT = -2;
    public static final int ACLS_NOT_SUPPORTED = -3;
    public static final int XATTRS_NOT_SUPPORTED = -4;
    public static final int UNKNOWN_ERROR = -999;
    public static final int MAX_CHUNKS_TOLERABLE_DEFAULT = 400;
    public static final int MAX_CHUNKS_IDEAL_DEFAULT = 100;
    public static final int MIN_RECORDS_PER_CHUNK_DEFAULT = 5;
    public static final int SPLIT_RATIO_DEFAULT = 2;

    public DsgDistCpConstants() {
    }
}
