/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.sybase;

import com.dsg.database.datasource.dto.DatasourceInfoImportVO;
import com.dtstack.dtcenter.common.loader.common.DtClassConsistent;
import com.dtstack.dtcenter.common.loader.common.utils.CollectionUtil;
import com.dtstack.dtcenter.common.loader.common.utils.DBUtil;
import com.dtstack.dtcenter.common.loader.rdbms.AbsRdbmsClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.DsIndexDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.RdbmsSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.SqlserverSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.SybaseJTDSSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.sql.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class SybaseClient extends AbsRdbmsClient {
    // 模糊查询数据库
    private static final String SHOW_DB_LIKE = "select * from master.dbo.sysdatabases where name like '%s'";

    private static final String TABLE_QUERY_ALL = "select table_name, table_schema from INFORMATION_SCHEMA.TABLES where table_type in ('VIEW', 'BASE TABLE')";
    private static final String TABLE_QUERY = "select table_name, table_schema from INFORMATION_SCHEMA.TABLES where table_type in ('BASE TABLE')";
    //查询字符集
    private static final String GET_SQLSERVER_CHARACTER_INFO = " SELECT collation_name  FROM sys.databases where name = '%s' ";

    private static final String SEARCH_BY_COLUMN_SQL = " and table_name like '%s' ";

    private static final String TABLE_SHOW = "\"%s\".\"%s\"";

    private static final String SYBASE_TABLE_ROW = "SELECT p.rows AS [Row Count]  FROM   sys.partitions p INNER JOIN sys.indexes i ON p.object_id = i.object_id AND p.index_id = i.index_id\n" +
            "WHERE  Object_schema_name(p.object_id) = '%s' AND Object_name(p.object_id) = '%s'";

    // 获取正在使用数据库
    private static final String CURRENT_DB = "SELECT db_name()";

    private static final String SEARCH_LIMIT_SQL = "select top %s table_name from INFORMATION_SCHEMA.TABLES where 1=1";
    private static final String SEARCH_SQL = "select table_name from INFORMATION_SCHEMA.TABLES where 1=1";
    private static final String SCHEMA_SQL = " and table_schema='%s'";
    private static final String TABLE_NAME_SQL = " and charIndex('%s',table_name) > 0";

    private static final String SCHEMAS_QUERY = "select user_name()";
    private static final String SYBASE_COLUMN_NAME = "column_name";
    private static final String SYBASE_COLUMN_COMMENT = "column_description";
    private static final String COLUMN_COMMENT_QUERY = "SELECT B.name AS column_name, C.value AS column_description FROM sys.tables A INNER JOIN sys.columns B ON B.object_id = A.object_id LEFT JOIN sys.extended_properties C ON C.major_id = B.object_id AND C.minor_id = B.column_id WHERE A.name = N";

    private static final String TABLE_COLUMN_COMMENT_QUERY = "SELECT B.name AS column_name, C.value AS column_description FROM (SELECT t2.object_id FROM sys.schemas as t1 INNER JOIN sys.tables as t2 on t1.schema_id = t2.schema_id and t1.name = N'%s' and t2.name = N'%s') A INNER JOIN sys.columns B ON B.object_id = A.object_id LEFT JOIN sys.extended_properties C ON C.major_id = B.object_id AND C.minor_id = B.column_id";


    private static final String COMMENT_QUERY = "select c.name, cast(isnull(f.[value], '') as nvarchar(100)) as REMARKS from sys.objects c left join sys.extended_properties f on f.major_id = c.object_id and f.minor_id = 0 and f.class = 1 where c.type = 'u' and c.name = N'%s'";
    private static final String TABLE_COMMENT_QUERY = "select c.name, cast(isnull(f.[value], '') as nvarchar(100)) as REMARKS from (SELECT t2.object_id FROM sys.schemas as t1 INNER JOIN sys.tables as t2 on t1.schema_id = t2.schema_id and t1.name = N'%s' and t2.name = N'%s') as a inner join sys.objects c ON c.object_id = a.object_id left join sys.extended_properties f on f.major_id = c.object_id and f.minor_id = 0 and f.class = 1 where c.type = 'u'";


    private static final Pattern TABLE_PATTERN = Pattern.compile("\\[(?<schema>(.*))]\\.\\[(?<table>(.*))]");

    // 获取当前版本号
    private static final String SHOW_VERSION = "SELECT @@VERSION";

    // 创建 schema
    private static final String CREATE_SCHEMA_SQL_TMPL = "create schema %s ";
    private static final String JDBC_URL = "****************************************";

    private static final String DONT_EXIST = "doesn't exist";

    private static final Map<Short, String> indexTypeMap = new HashMap<Short, String>() {
        {
            put((short) 0, "tableIndexStatistic");
            put((short) 1, "tableIndexClustered");
            put((short) 2, "tableIndexHashed");
            put((short) 3, "tableIndexOther");
        }
    };
    private static final String TABLE_IN_SCHEMA = "select table_name, table_schema from INFORMATION_SCHEMA.TABLES where table_schema ='%s' and table_name='%s'";
    @Override
    public String getTableExistSql(ISourceDTO source,SqlQueryDTO queryDTO) {
        String sql = String.format(TABLE_IN_SCHEMA, queryDTO.getSchema(), queryDTO.getTableName());
        return sql;
    }
    @Override
    protected ConnFactory getConnFactory() {
        return new SybaseConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.Sybase_jTDS;
    }

    @Override
    public List<String> getTableList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        SqlserverSourceDTO sqlserverSourceDTO = (SqlserverSourceDTO) iSource;
        String schema = StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : sqlserverSourceDTO.getSchema();
        Integer clearStatus = beforeQuery(sqlserverSourceDTO, queryDTO, false);

        Statement statement = null;
        ResultSet rs = null;
        List<String> tableList = new ArrayList<>();
        try {
            String sql = queryDTO.getView() ? TABLE_QUERY_ALL : TABLE_QUERY;
            if (StringUtils.isNotBlank(queryDTO.getTableNamePattern())) {
                sql = sql + String.format(SEARCH_BY_COLUMN_SQL, addFuzzySign(queryDTO));
            }
            // 查询schema下的
            if (StringUtils.isNotBlank(schema)) {
                sql += String.format(SCHEMA_SQL, schema);
            }
            statement = sqlserverSourceDTO.getConnection().createStatement();
            if (Objects.nonNull(queryDTO.getLimit())) {
                statement.setMaxRows(queryDTO.getLimit());
            }
            DBUtil.setFetchSize(statement, queryDTO);
            rs = statement.executeQuery(sql);
            while (rs.next()) {
                String tableName = StringUtils.isNotBlank(schema) ? rs.getString(1) :
                        String.format(TABLE_SHOW, rs.getString(2), rs.getString(1));
                tableList.add(tableName);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table exception,%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(sqlserverSourceDTO, clearStatus));
        }
        return tableList;
    }

    @Override
    public String getCharacterSet(ISourceDTO source, SqlQueryDTO queryDTO) {
        SqlserverSourceDTO sqlserverSourceDTO = (SqlserverSourceDTO) source;
        Integer clearStatus = beforeQuery(sqlserverSourceDTO, queryDTO, false);
        String currentDatabase = getCurrentDatabase(source);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = sqlserverSourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format(GET_SQLSERVER_CHARACTER_INFO, currentDatabase));
            while (resultSet.next()) {
                return resultSet.getString(1);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("获取sqlSever系统参数异常，%s",
                    e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sqlserverSourceDTO, clearStatus));
        }
        return "";
    }

    @Override
    public Long getTableRows(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        SqlserverSourceDTO sqlserverSourceDTO = (SqlserverSourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(sqlserverSourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;
        long tableRow = 0L;
        try {
            statement = sqlserverSourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format(SYBASE_TABLE_ROW, queryDTO.getSchema(), queryDTO.getTableName()));
            while (resultSet.next()) {
                tableRow = resultSet.getInt(1);
                return tableRow;
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Get table count exception：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sqlserverSourceDTO, clearStatus));
        }
        return tableRow;
    }

    @Override
    public String getTableMetaComment(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        SqlserverSourceDTO sqlserverSourceDTO = (SqlserverSourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(sqlserverSourceDTO, queryDTO);

        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = sqlserverSourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(queryTableCommentSql(queryDTO.getTableName()));
            if (resultSet.next()) {
                return resultSet.getString(DtClassConsistent.PublicConsistent.REMARKS);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table: %s's information error. Please contact the DBA to check the database、table information.",
                    queryDTO.getTableName()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sqlserverSourceDTO, clearStatus));
        }
        return null;
    }

    @Override
    public List<ColumnMetaDTO> getColumnMetaData(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(iSource, queryDTO);
        SqlserverSourceDTO rdbmsSourceDTO = (SqlserverSourceDTO) iSource;
        Set<ColumnMetaDTO> columns = new HashSet<>();
        List<ColumnMetaDTO> newColumns = new ArrayList<>();
        Statement statement = null;
        ResultSet rs = null;
        //修改 dgr 20220722
        ResultSet rsColumn = null;
        ResultSet pkRs = null;
        ResultSet fkRs = null;
        ResultSet uniqueRs = null;
        ResultSet allIndexRs = null;

        try {
            log.info("------------------开始getColumnMetaData------------------");
            rdbmsSourceDTO.setConnection(getTransConn(rdbmsSourceDTO.getConnection()));
            DatabaseMetaData metaData = rdbmsSourceDTO.getConnection().getMetaData();
            log.info("------------------start------------------");

            String catalog = rdbmsSourceDTO.getConnection().getCatalog();
            if(StringUtils.isNotEmpty(queryDTO.getDbName())){
                catalog=queryDTO.getDbName();
            }
            pkRs = metaData.getPrimaryKeys(catalog, queryDTO.getSchema(), queryDTO.getTableName());
            ArrayList<String> pkList = new ArrayList<>();
            while (pkRs.next()) {
                pkList.add(pkRs.getString("COLUMN_NAME"));
            }

            log.info("------------------执行pkRs结束------------------");

            fkRs = metaData.getExportedKeys(catalog, queryDTO.getSchema(), queryDTO.getTableName());
            ArrayList<String> fkList = new ArrayList<>();
            while (fkRs.next()) {
                fkList.add(fkRs.getString("PKCOLUMN_NAME"));
            }

            log.info("------------------执行fkRs结束------------------");

            //Sybase表名为小写的时候不支持查询索引
            ArrayList<String> uniqueList = new ArrayList<>();
            ArrayList<DsIndexDTO> allIndexList = new ArrayList<>();
            log.info("------------------sybase表名为小写的时候不支持查询索引------------------");

            rsColumn = metaData.getColumns(catalog, queryDTO.getSchema(), queryDTO.getTableName(), null);
            while (rsColumn.next()) {
                ColumnMetaDTO columnMetaDTO = new ColumnMetaDTO();
                columnMetaDTO.setPart(false);
                columnMetaDTO.setKey(rsColumn.getString("COLUMN_NAME"));
                columnMetaDTO.setType(rsColumn.getString("TYPE_NAME"));
                columnMetaDTO.setComment(rsColumn.getString("REMARKS"));
                columnMetaDTO.setScale(rsColumn.getInt("DECIMAL_DIGITS"));
                columnMetaDTO.setLength(rsColumn.getInt("COLUMN_SIZE"));
                columnMetaDTO.setDataType(rsColumn.getInt("DATA_TYPE"));
                columnMetaDTO.setDefaultValue(rsColumn.getString("COLUMN_DEF"));
                columnMetaDTO.setNotNullFlag("no".equals(rsColumn.getString("IS_NULLABLE").toLowerCase()));
                if (pkList.contains(rsColumn.getString("COLUMN_NAME"))) {
                    columnMetaDTO.setPkflag(true);
                } else {
                    columnMetaDTO.setPkflag(false);
                }
                if (fkList.contains(rsColumn.getString("COLUMN_NAME"))) {
                    columnMetaDTO.setFkflag(true);
                } else {
                    columnMetaDTO.setFkflag(false);
                }
                if (uniqueList.contains(rsColumn.getString("COLUMN_NAME"))) {
                    columnMetaDTO.setUniqueFlag(true);
                } else {
                    columnMetaDTO.setUniqueFlag(false);
                }

                for (DsIndexDTO dsIndexDTO : allIndexList
                ) {
                    if (rsColumn.getString("COLUMN_NAME").equals(dsIndexDTO.getColumnName())) {
                        columnMetaDTO.setIndexType(indexTypeMap.get(dsIndexDTO.getType()));
                    }
                }

                columns.add(columnMetaDTO);
            }

            log.info("------------------执行getColumns结束------------------");

            statement = rdbmsSourceDTO.getConnection().createStatement();
            statement.setMaxRows(1);
            String queryColumnSql =
                    "select " + CollectionUtil.listToStr(queryDTO.getColumns()) + " from "+catalog+"." + transferSchemaAndTableName(rdbmsSourceDTO, queryDTO) + " where 1=2";

            rs = statement.executeQuery(queryColumnSql);

            log.info("------------------queryColumnSql{}------------------",queryColumnSql);
            log.info("------------------执行select结束------------------");

            ResultSetMetaData rsMetaData = rs.getMetaData();
            int columnCount = rsMetaData.getColumnCount();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = rsMetaData.getColumnName(i);
                for (ColumnMetaDTO columnMetaDTO : columns) {
                    if (columnMetaDTO.getKey().equals(columnName)) {
                        columnMetaDTO.setPrecision(rsMetaData.getPrecision(i));
                        columnMetaDTO.setDateType(rsMetaData.getColumnClassName(i));
                        newColumns.add(columnMetaDTO);
                    }
                }
            }
            Map<String, String> columnComments = getColumnComments((RdbmsSourceDTO) iSource, queryDTO);
            for (ColumnMetaDTO columnMetaDatum : newColumns) {
                columnMetaDatum.setComment(columnComments.get(columnMetaDatum.getKey()));
            }

        } catch (SQLException e) {
            log.error("------------------表{}，字段采集报错{}------------------",queryDTO.getTableName(),e.getMessage());
            if (e.getMessage().contains(DONT_EXIST)) {
                throw new DtLoaderException(String.format(queryDTO.getTableName() + "table not exist,%s", e.getMessage()), e);
            } else {
                throw new DtLoaderException(String.format("Failed to get the meta information of the fields of the table: %s. Please contact the DBA to check the database and table information: %s",
                        queryDTO.getTableName(), e.getMessage()), e);
            }
        } finally {
            DBUtil.closeDBResources(pkRs, null, null);
            DBUtil.closeDBResources(fkRs, null, null);
            DBUtil.closeDBResources(uniqueRs, null, null);
            DBUtil.closeDBResources(allIndexRs, null, null);
            DBUtil.closeDBResources(rsColumn, null, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return newColumns;
    }

    @Override
    public IDownloader getDownloader(ISourceDTO source, SqlQueryDTO queryDTO) throws Exception {
        SybaseJTDSSourceDTO sybaseSourceDTO = (SybaseJTDSSourceDTO) source;
        String schema = StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : sybaseSourceDTO.getSchema();
        SybaseDownloader sybaseDownloader = new SybaseDownloader(getCon(sybaseSourceDTO), queryDTO.getSql(), schema);
        sybaseDownloader.configure();
        return sybaseDownloader;
    }

    @Override
    protected String transferSchemaAndTableName(String schema, String tableName) {
        //如果传过来是[tableName]格式直接当成表名
        if (tableName.startsWith("\"") && tableName.endsWith("\"")) {
            if (StringUtils.isNotBlank(schema)) {
                return String.format("%s.%s", schema, tableName);
            }
            return tableName;
        }
        //如果不是上述格式，判断有没有"."符号，有的话，第一个"."之前的当成schema，后面的当成表名进行[tableName]处理
        if (tableName.contains(".")) {
            //切割，表名中可能会有包含"."的情况，所以限制切割后长度为2
            String[] tables = tableName.split("\\.", 2);
            tableName = tables[1];
            return String.format("%s.%s", tables[0], tableName.contains("\"") ? tableName : String.format("\"%s\"",
                    tableName));
        }
        //判断表名
        if (StringUtils.isNotBlank(schema)) {
            return String.format("\"%s\".\"%s\"", schema, tableName);
        }
        return String.format("\"%s\"", tableName);
    }

    @Override
    protected String getTableBySchemaSql(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        StringBuilder constr = new StringBuilder();
        if (queryDTO.getLimit() != null) {
            constr.append(String.format(SEARCH_LIMIT_SQL, queryDTO.getLimit()));
        } else {
            constr.append(SEARCH_SQL);
        }
        //判断是否需要schema
        if (StringUtils.isNotBlank(queryDTO.getSchema())) {
            constr.append(String.format(SCHEMA_SQL, queryDTO.getSchema()));
        }
        // 根据name 模糊查询
        if (StringUtils.isNotBlank(queryDTO.getTableNamePattern())) {
            constr.append(String.format(TABLE_NAME_SQL, queryDTO.getTableNamePattern()));
        }
        return constr.toString();
    }

    @Override
    public String getCreateTableSql(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException("Not Support");
    }

    @Override
    public List<ColumnMetaDTO> getPartitionColumn(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException("Not Support");
    }

    @Override
    protected Map<String, String> getColumnComments(RdbmsSourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(sourceDTO, queryDTO);
        Statement statement = null;
        ResultSet rs = null;
        Map<String, String> columnComments = new HashMap<>();
        try {
            statement = sourceDTO.getConnection().createStatement();
            String queryColumnCommentSql = queryColumnCommentSql(queryDTO.getTableName());
            rs = statement.executeQuery(queryColumnCommentSql);
            while (rs.next()) {
                String columnName = rs.getString(SYBASE_COLUMN_NAME);
                String columnComment = rs.getString(SYBASE_COLUMN_COMMENT);
                columnComments.put(columnName, columnComment);
            }

        } catch (Exception e) {
            //获取表字段注释失败
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(sourceDTO, clearStatus));
        }
        return columnComments;
    }

    /**
     * 查询表字段注释
     *
     * @param tableName
     * @return
     */
    private static String queryColumnCommentSql(String tableName) {
        String queryColumnCommentSql;
        Matcher matcher = TABLE_PATTERN.matcher(tableName);
        //兼容 [schema].[table] 情况
        if (matcher.find()) {
            String schema = matcher.group("schema");
            String table = matcher.group("table");
            queryColumnCommentSql = String.format(TABLE_COLUMN_COMMENT_QUERY, schema, table);
        } else {
            queryColumnCommentSql = COLUMN_COMMENT_QUERY + addSingleQuotes(tableName);
        }
        return queryColumnCommentSql;
    }

    /**
     * 查询表注释sql
     *
     * @param tableName
     * @return
     */
    private static String queryTableCommentSql(String tableName) {
        Matcher matcher = TABLE_PATTERN.matcher(tableName);
        //兼容 [schema].[table] 情况
        if (matcher.find()) {
            String schema = matcher.group("schema");
            String table = matcher.group("table");
            return String.format(TABLE_COMMENT_QUERY, schema, table);
        }
        return String.format(COMMENT_QUERY, tableName);
    }

    private static String addSingleQuotes(String str) {
        str = str.contains("'") ? str : String.format("'%s'", str);
        return str;
    }

    @Override
    public String getShowDbSql() {
        return SCHEMAS_QUERY;
    }

    @Override
    protected String getCurrentDbSql() {
        return CURRENT_DB;
    }

    @Override
    public String getVersionSql() {
        return SHOW_VERSION;
    }

    @Override
    protected String getCreateDatabaseSql(String dbName, String comment) {
        return String.format(CREATE_SCHEMA_SQL_TMPL, dbName);
    }

    @Override
    public String getCharacterCollation(ISourceDTO source, SqlQueryDTO queryDTO) {
        return getCharacterSet(source, queryDTO);
    }

    @Override
    public Boolean isDatabaseExists(ISourceDTO source, String dbName) {
        if (StringUtils.isBlank(dbName)) {
            throw new DtLoaderException("database name is not empty");
        }
        return CollectionUtils.isNotEmpty(executeQuery(source, SqlQueryDTO.builder().sql(String.format(SHOW_DB_LIKE, dbName)).build()));
    }

    @Override
    protected String doDealType(ResultSetMetaData rsMetaData, Integer los) throws SQLException {
        String typeName = rsMetaData.getColumnTypeName(los + 1);
        // 适配 Sybase 自增字段，返回值类型如 -> int identity，只需要返回 int
        if (StringUtils.containsIgnoreCase(typeName, "identity") && typeName.split(" ").length > 1) {
            return typeName.split(" ")[0];
        }
        return typeName;
    }

    @Override
    protected String getDbSeparator() {
        return "\"";
    }

    @Override
    protected Pair<Character, Character> getSpecialSign() {
        return Pair.of('[', ']');
    }

    protected String getJdbcUrl( DatasourceInfoImportVO datasourceInfoImportVO) {
        String jdbcUrl = String.format(JDBC_URL, datasourceInfoImportVO.getIp(), datasourceInfoImportVO.getPort(),datasourceInfoImportVO.getDbName());
        return jdbcUrl;
    }
}
