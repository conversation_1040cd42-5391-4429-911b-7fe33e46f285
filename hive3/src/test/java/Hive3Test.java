/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import com.dtstack.dtcenter.common.loader.hive3.HiveConnFactory;
import com.dtstack.dtcenter.common.loader.hive3.client.Hive3Client;
import com.dtstack.dtcenter.common.loader.hive3.client.Hive3TableClient;
import com.dtstack.dtcenter.loader.client.IClient;
import com.dtstack.dtcenter.loader.client.ITable;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.Hive3SourceDTO;
import com.dtstack.dtcenter.loader.utils.AssertUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.util.List;


public class Hive3Test {


    @Test
    public void getCon() throws Exception {
        Hive3SourceDTO source = Hive3SourceDTO.builder()
                .url("****************************************")
                .defaultFS("hdfs://master.hdp32.com:8020")
                .build();

        HiveConnFactory connFactory = new HiveConnFactory();
        connFactory.getConn(source, StringUtils.EMPTY);
    }

    @Test
    public void getTableMetaComment() throws Exception {
        Hive3SourceDTO source = Hive3SourceDTO.builder()
                .url("****************************************")
                .defaultFS("hdfs://master.hdp32.com:8020")
                .username("hdfs")
                .schema("default")
                .build();
        IClient client = new Hive3Client();
        SqlQueryDTO build = SqlQueryDTO.builder().
                tableName("test_zhongjiao_parquet_partition02").build();
        String tableMetaComment = client.getTableMetaComment(source, build);
        //List<String> list = client.getTableList(source, SqlQueryDTO.builder().build());
        AssertUtils.notNull(tableMetaComment, "");
    }

    @Test
    public void getTableSize() throws Exception {
        Hive3SourceDTO source = Hive3SourceDTO.builder()
                .url("****************************************")
                .defaultFS("hdfs://master.hdp32.com:8020")
                .username("hdfs")
                .schema("default")
                .build();
        ITable table = new Hive3TableClient();
        Long huahua = table.getTableSize(source, "", "huahua");
        AssertUtils.notNull(huahua, "");
    }

    @Test
    public void getTableRows() throws Exception {
        Hive3SourceDTO source = Hive3SourceDTO.builder()
                .url("****************************************")
                .defaultFS("hdfs://master.hdp32.com:8020")
                .username("hdfs")
                .schema("default")
                .build();
        IClient client = new Hive3Client();
        SqlQueryDTO build = SqlQueryDTO.builder().
                tableName("huahua").build();
        Long tableRows = client.getTableRows(source, build);
        AssertUtils.notNull(tableRows, "");
    }

    @Test
    public void getColumnMetaData() throws Exception {
        Hive3SourceDTO source = Hive3SourceDTO.builder()
                .url("*******************************************")
                .defaultFS("hdfs://master.hdp32.com:8020")
                .username("hdfs")
                .schema("gmall_hive")
                .build();
        IClient client = new Hive3Client();
        SqlQueryDTO build = SqlQueryDTO.builder().
                tableName("dwd_order_detail").build();
        List<ColumnMetaDTO> columnMetaData = client.getColumnMetaData(source, build);
        for (int i = 0; i < columnMetaData.size(); i++) {
            System.out.println(columnMetaData.get(i).toString());
        }
        AssertUtils.notNull(columnMetaData, "");
    }



}
