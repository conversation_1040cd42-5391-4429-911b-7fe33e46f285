package com.dtstack.dtcenter.common.loader.postgresql;

public class SqlConstants {

    //函数常量
    public static final String FUNCTION_TYPE = "FUNCTION";
    //存储过程
    public static final String  PROCEDURE_TYPE = "PROCEDURE";
    //表
    public static final String TABLE_TYPE = "TABLE";
    //视图
    public static final String VIEW_TYPE = "VIEW";
    // 视图
    public static final String VIEW = "'VIEW'";

    // 普通表
    public static final String BASE_TABLE = "'BASE TABLE'";

    // 获取指定数据库下的表
    public static final String GET_TABLE_SCHEMA_SQL = "SELECT \n" +
            "    t.table_name, \n" +
            "    t.table_type, \n" +
            "    obj_description(('\"' || t.table_schema || '\".\"' || t.table_name || '\"')::regclass, 'pg_class') AS table_comment\n" +
            "FROM information_schema.tables t\n" +
            "WHERE t.table_schema = '%s' \n" +
            "AND t.table_type = %s %s";

    //表名模糊搜索sql
    // 表名正则匹配模糊查询
    public static final String SEARCH_SQL = " AND t.table_name LIKE  '%%%s%%'  ";


    //获取指定库下或者指定表下的索引
    public static final String GET_INDEX_SQL = "SELECT \n" +
            "    schemaname AS table_schema,\n" +
            "    indexname AS index_name,\n" +
            "    tablename AS table_name,\n" +
            "    indexdef AS index_definition\n" +
            "FROM pg_indexes\n" +
            "WHERE schemaname = '%s' \n";

    /**
     * 索引搜索sql
     */
    public static final String SEARCH_INDEX_SQL = " AND index_name LIKE  '%%%s%%'  ";

    /**
     * 索引分组sql、
     */
    public static final String GROUP_INDEX_SQL = " GROUP BY TABLE_SCHEMA,TABLE_NAME,INDEX_NAME,NON_UNIQUE,INDEX_TYPE,INDEX_COMMENT";





    //获取指定库下指定表的索引
    public static final String GET_INDEX_SQL_BY_TABLE = "";

    //获取索引字段列sql
    public static final String GET_INDEX_COLUMN_SQL = "SELECT \n" +
            "    ix.relname AS index_name,           \n" +
            "    t.relname AS table_name,          \n" +
            "    a.attname AS column_name,          \n" +
            "    array_position(i.indkey, a.attnum) AS seq_in_index,\n" +
            "    am.amname AS index_type,            \n" +
            "    NOT a.attnotnull AS is_nullable,   \n" +
            "    i.indisunique AS is_unique         \n" +
            "FROM pg_class t\n" +
            "JOIN pg_index i ON t.oid = i.indrelid\n" +
            "JOIN pg_class ix ON i.indexrelid = ix.oid\n" +
            "JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(i.indkey)  \n" +
            "JOIN pg_namespace ns ON ns.oid = t.relnamespace  \n" +
            "JOIN pg_am am ON ix.relam = am.oid  \n" +
            "WHERE ns.nspname = '%s'  \n" +
            "AND t.relname = '%s' \n" +
            "AND ix.relname = '%s'" ;


    //获取指定库得函数或者存储过程
    public static final String GET_PRODUCE_SQL = "SELECT * "+
            "FROM INFORMATION_SCHEMA.ROUTINES\n" +
            "WHERE ROUTINE_SCHEMA = '%s'  AND ROUTINE_NAME='%s'" ;

    public static final String SEARCH_PRODUCE_SQL = " AND ROUTINE_NAME LIKE  '%%%s%%'  ";

    //获取指定库得函数或者存储过程参数
    public static final String GET_PRODUCE_ARGUMENTS_SQL = "select \n" +
            "regexp_replace(a.specific_name, E'_[0-9]+$', '') AS function_name,\n" +
            "a.parameter_name,\n" +
            "a.data_type, \n" +
            "a.ordinal_position,\n" +
            "a.character_maximum_length,\n" +
            "a.parameter_mode \n" +
            "from information_schema.parameters a\n" +
            "where a.parameter_name is not null\n" +
            "and a.specific_schema ='%s' and  regexp_replace(a.specific_name, E'_[0-9]+$', '') = '%s'";

    public static final String ALL_PROCEDURES = "SELECT * FROM INFORMATION_SCHEMA.ROUTINES WHERE  routine_schema = '%s'";

    /**
     * 查询创建函数语句
     */
    public static final String CREATE_FUNCTION_SQL="SELECT\n" +
            "    pg_get_functiondef(p.oid) AS ddl\n" +
            "FROM\n" +
            "    pg_proc p\n" +
            "JOIN\n" +
            "    pg_namespace n ON p.pronamespace = n.oid\n" +
            "WHERE\n" +
            "    p.proname = '%s'\n" +
            "    AND n.nspname = '%s'";
}
