sonar.projectKey=dt_center_common_loader
sonar.projectVersion=4.2.0
#sonar.tests=src/test
# \u6E90\u7801\u6240\u5728\u4F4D\u7F6E
sonar.sources=.
# \u6392\u9664test\u6A21\u5757\u3001mysql8\u6A21\u5757\u3001hbase2\u6A21\u5757\u3001hbase_gateway\u6A21\u5757\u3001s3\u6A21\u5757\uFF0Chive\u3001hdfs\u7B49\u6E90\u7801\u6587\u4EF6
sonar.exclusions=test/**,mysql8/**,hive3/**,sqlserver2017/**,hbase2/**,hbase_gateway/**,dm/**,emq/**,vertica/**,websocket/**,socket/**,s3/**,**/src/main/java/org/**
# \u5B57\u8282\u7801\u6587\u4EF6\u6240\u5728\u4F4D\u7F6E
sonar.java.binaries=.
sonar.language=java
sonar.sourceEncoding=UTF-8

# \u6307\u5B9A\u4EE3\u7801\u8986\u76D6\u7387\u5DE5\u5177
sonar.core.codeCoveragePlugin=jacoco
sonar.xml.enabled=false
sonar.tests=test
sonar.test.inclusions=.
# \u53BB\u9664\u4E0D\u9700\u8981\u5355\u6D4B\u8986\u76D6\u7684\u7C7B
sonar.test.exclusions=test/**,mysql8/**,hive3/**,sqlserver2017/**,hbase2/**,hbase_gateway/**,dm/**,emq/**,vertica/**,websocket/**,socket/**,s3/**,**/src/main/java/org/**


# sonar \u5355\u6D4B\u6267\u884C\u60C5\u51B5\u5730\u5740
sonar.surefire.reportsPath=test/target/surefire-reports
sonar.dynamicAnalysis=reuseReports
# xml \u5730\u5740
sonar.coverage.jacoco.xmlReportPaths=test/target/site/jacoco-aggregate/jacoco.xml
