package com.joyadata.dsc.datasource.service;

import com.joyadata.dsc.datasource.constants.Constants;
import com.joyadata.dsc.datasource.model.datasoure.DatasourceAuthorization;
import com.joyadata.dsc.datasource.model.datasoure.DatasourceGrantScope;
import com.joyadata.service.BaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Service
public class DatasourceAuthorizationService extends BaseService<DatasourceAuthorization> {

    /**
     * 保存数据源授权
     * @param authorizations 授权信息
     * @return 保存结果
     */
    public String saveAuthorization(List<DatasourceAuthorization> authorizations) {
        List<DatasourceGrantScope> grantScopeList = new ArrayList<>();
        for (DatasourceAuthorization authorization : authorizations) {
            // 项目没有 metadataFullAccess 特殊处理
            if (DatasourceAuthorization.PrincipalType.PROJECT.equals(authorization.getPrincipalType())) {
                // 指定库
                if (DatasourceAuthorization.ScopeType.SPECIFIC.equals(authorization.getScopeType())) {
                    authorization.getDatasourceGrantScope();
                }
            } else {
                if (authorization.getMetadataFullAccess().equals(Constants.NO)) {
                    authorization.getDatasourceGrantScope();
                }
            }
        }
        return null;
    }
}
