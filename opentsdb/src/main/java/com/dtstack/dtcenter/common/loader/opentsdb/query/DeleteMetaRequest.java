/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.opentsdb.query;

import java.util.List;
import java.util.Map;

public class DeleteMetaRequest extends Timeline {

    private boolean deleteData;

    private boolean recursive;

    public DeleteMetaRequest(String metric, Map<String, String> tags, boolean deleteData, boolean recursive) {
        super(metric, tags);
        this.deleteData = deleteData;
        this.recursive = recursive;
    }

    public DeleteMetaRequest(String metric, Map<String, String> tags, List<String> fields, boolean deleteData, boolean recursive) {
        super(metric, tags, fields);
        this.deleteData = deleteData;
        this.recursive = recursive;
    }

    public boolean isDeleteData() {
        return deleteData;
    }

    public void setDeleteData(boolean deleteData) {
        this.deleteData = deleteData;
    }

    public boolean isRecursive() {
        return recursive;
    }

    public void setRecursive(boolean recursive) {
        this.recursive = recursive;
    }
}
