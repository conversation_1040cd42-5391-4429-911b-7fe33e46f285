## s3 client

### 一、插件包名称
名称：**s3**

### 二、对应数据源sourceDTO及参数说明

[S3SourceDTO](/core/src/main/java/com/dtstack/dtcenter/loader/dto/source/S3SourceDTO.java)

参数说明：


- **username**
  - 描述：数据源的用户名
  - 必选：否
  - 默认值：无



- **password**
  - 描述：数据源指定用户名的密码
  - 必选：否
  - 默认值：无



- **hostname**
  - 描述：域名信息
  - 必选：否
  - 默认值：无
  
#### 三、支持的方发及使用demo

##### IClient客户端使用

构造sourceDTO

```$java
        S3SourceDTO source = S3SourceDTO.builder()
                    .username("xxx")
                    .password("xxxx")
                    .hostname("xxxx")
                    .build();
```

###### 1. 校验数据源连通性
入参类型：
- S3SourceDTO：数据源连接信息

出参类型：
- Boolean：是否连通

使用：
```$java
        IClient client = ClientCache.getClient(DataSourceType.S3.getVal());
        Boolean isConnected = client.testCon(sourceDTO);
```
