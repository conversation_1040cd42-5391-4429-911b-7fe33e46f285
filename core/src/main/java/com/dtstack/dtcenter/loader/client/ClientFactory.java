/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.client;

//import com.alibaba.fastjson.serializer.DateCodec;
//import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.writer.ObjectWriter;
import com.dtstack.dtcenter.loader.DtClassLoader;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 15:36 2020/8/19
 * @Description：客户端工厂抽象类
 */
@Slf4j
public class ClientFactory {
    /**
     * 存储 插件名称 - ClassLoader 键值对信息
     */
    private static final Map<String, ClassLoader> PLUGIN_CLASSLOADER = Maps.newConcurrentMap();

    /**
     * 获取类加载器
     *
     * @param pluginName
     * @return
     * @throws Exception
     */
    public static ClassLoader getClassLoader(String pluginName) throws Exception {
        ClassLoader classLoader = PLUGIN_CLASSLOADER.get(pluginName);
        if (null != classLoader) {
            return classLoader;
        }

        synchronized (ClientFactory.class) {
            classLoader = PLUGIN_CLASSLOADER.get(pluginName);
            if (classLoader == null) {
                classLoader = getClassLoad(pluginName, getFileByPluginName(pluginName));
                PLUGIN_CLASSLOADER.put(pluginName, classLoader);
            }
        }

        dealFastJSON(pluginName, classLoader);
        return classLoader;
    }

    /**
     * 校验是否存在
     *
     * @param pluginName
     * @return
     */
    public static boolean checkContainClassLoader(String pluginName) {
        return PLUGIN_CLASSLOADER.containsKey(pluginName);
    }

    /**
     * 根据文件流生成当前的 ClassLoader
     *
     * @param file
     * @return
     * @throws MalformedURLException
     */
    private static URLClassLoader getClassLoad(String pluginName, @NotNull File file) throws MalformedURLException {
        File[] files = file.listFiles();
        List<URL> urlList = new ArrayList<>();
        if (files.length == 0) {
            throw new DtLoaderException("The plugin folder setting is abnormal, please handle it again");
        }

        for (File f : files) {
            String jarName = f.getName();
            if (f.isFile() && jarName.endsWith(".jar")) {
                log.info("Data source plugin pulls Jar package, plugin name: {}, Jar package name: {}", pluginName, jarName);
                urlList.add(f.toURI().toURL());
            }
        }

        return new DtClassLoader(urlList.toArray(new URL[urlList.size()]), Thread.currentThread().getContextClassLoader());
    }

//    /**
//     * 特殊处理序列化逻辑
//     */
//    private static void dealFastJSON(String pluginName, ClassLoader classLoader) {
//        // 处理 oracle 时间类型字段
//        if (DataSourceType.Oracle.getPluginName().equals(pluginName)) {
//            try {
//                Class<?> loadClass = classLoader.loadClass("oracle.sql.DATE");
//                SerializeConfig.getGlobalInstance().put(loadClass, DateTimeCodec.instance);
//                loadClass = classLoader.loadClass("oracle.sql.TIMESTAMP");
//                SerializeConfig.getGlobalInstance().put(loadClass, DateCodec.instance);
//            } catch (ClassNotFoundException e) {
//                log.error("FastJSON Serialization tool exception", e);
//            }
//        }
//    }

    /**
     * 特殊处理序列化逻辑
     * 当使用 FastJSON 2 作为序列化工具，并且数据源类型为 Oracle 时，
     * 需要对 Oracle 的时间类型字段进行特殊处理，以确保序列化正确
     *
     * 在处理 Oracle 的 DATE 和 TIMESTAMP 类型时，进行特殊序列化的原因主要有以下几点：
     * 默认序列化行为不满足需求：
     * Oracle 的 DATE 和 TIMESTAMP 类型在默认情况下可能不会被 FastJSON 正确序列化为所需的格式。默认的序列化可能会导致日期格式不一致或不完整，从而影响数据的正确性和可读性。
     * 兼容性和标准化：
     * 通过自定义序列化逻辑，可以确保所有日期和时间数据在序列化和反序列化过程中保持一致的格式。这对于跨系统或跨服务的数据交换尤为重要。
     * 避免时区问题：
     * Oracle 的 DATE 和 TIMESTAMP 类型可能包含时区信息。默认的序列化逻辑可能无法正确处理时区，导致数据在不同系统之间传递时出现时区偏差。自定义序列化可以确保时区信息的正确处理。
     * 性能优化：
     * 自定义序列化逻辑可以针对特定的数据类型进行优化，提高序列化和反序列化的性能。
     * 数据完整性：
     * 自定义序列化可以确保所有日期和时间数据在序列化过程中不丢失任何信息，保持数据的完整性。
     *
     * @param pluginName 数据源插件名称，用于判断是否需要进行特殊处理
     * @param classLoader 类加载器，用于加载特定的数据源类
     */
    private static void dealFastJSON(String pluginName, ClassLoader classLoader) {
        // 处理 oracle 时间类型字段
        if (DataSourceType.Oracle.getPluginName().equals(pluginName) ||
                DataSourceType.Oracle_19c.getPluginName().equals(pluginName) ||
                DataSourceType.Oracle_9i.getPluginName().equals(pluginName)) {
            try {
                // 加载 Oracle 的 DATE 类并配置 FastJSON 2 的序列化方式
                Class<?> oracleDateClass = classLoader.loadClass("oracle.sql.DATE");
                // 加载 Oracle 的 TIMESTAMP 类并配置 FastJSON 2 的序列化方式
                Class<?> oracleTimestampClass = classLoader.loadClass("oracle.sql.TIMESTAMP");

                // 注册自定义的序列化器
                JSON.register(oracleDateClass, (ObjectWriter) (jsonWriter, object, fieldName, fieldType, features) -> {
                    // 自定义序列化逻辑
                    jsonWriter.writeString(object.toString());
                });
                JSON.register(oracleTimestampClass, (ObjectWriter) (jsonWriter, object, fieldName, fieldType, features) -> {
                    // 自定义序列化逻辑
                    jsonWriter.writeString(object.toString());
                });
            } catch (ClassNotFoundException e) {
                // 当找不到 Oracle 的时间类型类时记录错误日志
                log.error("FastJSON Serialization tool exception", e);
            }
        }
    }

    /**
     * 根据插件名称获取文件
     *
     * @param pluginName
     * @return
     * @throws Exception
     */
    @NotNull
    private static File getFileByPluginName(String pluginName) throws Exception {
        String plugin = String.format("%s/%s", ClientCache.getUserDir(), pluginName).replaceAll("//*", "/");
        File finput = new File(plugin);
        if (!finput.exists()) {
            throw new Exception(String.format("%s directory not found", plugin));
        }
        return finput;
    }
}
