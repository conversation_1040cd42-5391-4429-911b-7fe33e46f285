package com.dtstack.dtcenter.loader.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * sql里面传入最基本的where拼接条件
 *
 * <AUTHOR>
 * @date 2023/6/1 14:49
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RelationConditionDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String EQUAL = "=";

    public static final String LIKE = "like";

    public static final String IN = "in";

    public static final String NOT_IN = "not in";

    public static final String NOT_EQUAL = "!=";

    public static final String GREATER_THAN = ">";

    public static final String GREATER_THAN_OR_EQUAL = ">=";

    public static final String LESS_THAN = "<";

    public static final String LESS_THAN_OR_EQUAL = "<=";

    public static final String BETWEEN = "between";

    public static final String NOT_BETWEEN = "not between";

    /**
     * 关联字段
     */
    private String columnName;

    /**
     * 关联值
     */
    private String columnValue;

    /**
     * 关联字段类型
     */
    private String columnType;

    /**
     * 关联字段搜索类型
     */
    private String columnSearchType;
}
