/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.client;

import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.dto.*;
import com.dtstack.dtcenter.loader.dto.*;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.KafkaSourceDTO;

import java.util.List;
import java.util.Map;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 下午2:08 2020/6/2
 * @Description：kafka客户端接口
 */
public interface IKafka<T> {

    /**
     * 校验 连接
     *
     * @param source
     * @return
     * @throws Exception
     */
    Boolean testCon(ISourceDTO source);

    /**
     * 获取所有 Brokers 的地址
     *
     * @param source
     * @return
     * @throws Exception
     */
    String getAllBrokersAddress(ISourceDTO source);

    /**
     * 获取所有 Brokers 的地址
     *
     * @param source
     * @return
     * @throws Exception
     */
    List<BrokersDTO> getAllBrokersInfoList(ISourceDTO source);


    /**
     * 获取 所有 Topic 信息
     *
     * @param source
     * @return
     * @throws Exception
     */
    List<String> getTopicList(ISourceDTO source);

    List<TopIcInfoDTO> getTopicInfoList(ISourceDTO source);


    /**
     * 获取特定 Topic 分区信息
     *
     * @param source
     * @param topic
     * @return
     * @throws Exception
     */
    @Deprecated
    List<T> getAllPartitions(ISourceDTO source, String topic);

    /**
     * 获取特定 Topic 所有分区的偏移量
     *
     * @param source
     * @param topic
     * @return
     * @throws Exception
     */
    List<KafkaOffsetDTO> getOffset(ISourceDTO source, String topic);

    /**
     * 获取预览数据
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     * @deprecated since 1.4.0 in favor of
     * {@link #getPreview(ISourceDTO source, SqlQueryDTO queryDTO, String prevMode)}
     */
    @Deprecated
    List<List<Object>> getPreview(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 获取预览数据
     *
     * @param source   数据源信息
     * @param queryDTO 查询条件
     * @param prevMode 预览模式
     * @return
     * @throws Exception
     */
    List<List<Object>> getPreview(ISourceDTO source, SqlQueryDTO queryDTO, String prevMode);

    List<List<Object>> getRecordsFromKafkaByStatistics(ISourceDTO sourceDTO,Integer partition, String topic, String autoReset, String maxpoolrecords);

    /**
     * 获取kafka指定topic下的分区信息
     *
     * @param source 数据源信息
     * @param topic  topic名称
     * @return 分区数量
     */
    List<KafkaPartitionDTO> getTopicPartitions(ISourceDTO source, String topic);

    /**
     * 从kafka 中消费数据
     *
     * @param source          数据源信息
     * @param topic           topic
     * @param collectNum      最大条数
     * @param offsetReset     从哪里开始消费
     * @param timestampOffset 消费启始位置
     * @param maxTimeWait     最大等待时间，单位秒
     * @return kafka数据
     */
    List<String> consumeData(ISourceDTO source, String topic, Integer collectNum, String offsetReset, Long timestampOffset, Integer maxTimeWait);

    /**
     * 获取所有的消费者组
     *
     * @param source 数据源信息
     * @return 消费者组列表
     */
    List<String> listConsumerGroup(ISourceDTO source);

    /**
     * 获取消费组info
     *
     * @param source
     * @return
     */
    List<ConsumerInfoDTO> getConsumerInfoList(ISourceDTO source);

    JSONObject listTopicInfoByGroupId(ISourceDTO sourceDTO, String groupId);

    /**
     * 获取指定topic下的所有的消费者组
     *
     * @param source 数据源信息
     * @return 消费者组列表
     */
    List<String> listConsumerGroupByTopic(ISourceDTO source, String topic);

    /**
     * 获取 kafka 消费者组详细信息
     *
     * @param source  数据源信息
     * @param groupId 消费者组
     * @return 消费者组详细信息
     */
    List<KafkaConsumerDTO> getGroupInfoByGroupId(ISourceDTO source, String groupId);

    /**
     * 获取 kafka 指定topic 下消费者组详细信息
     *
     * @param source 数据源信息
     * @param topic  kafka主题
     * @return 消费者组详细信息
     */
    List<KafkaConsumerDTO> getGroupInfoByTopic(ISourceDTO source, String topic);

    /**
     * 获取 kafka 指定topic下指定消费者组详细信息
     *
     * @param source  数据源信息
     * @param groupId 消费者组
     * @param topic   kafka主题
     * @return 消费者组详细信息
     */
    List<KafkaConsumerDTO> getGroupInfoByGroupIdAndTopic(ISourceDTO source, String groupId, String topic);

    /**
     * 获取broker LeaderSkewed
     *
     * @param source
     * @param topic
     * @return
     */
    Integer getBrokerLeaderSkewed(ISourceDTO source, String topic);

    /**
     * 获取broker Skewed
     *
     * @param source
     * @param topic
     * @return
     */
    Integer getBrokerSkewed(ISourceDTO source, String topic);

    /**
     * 获取broker Spread
     *
     * @param source
     * @param topic
     * @return
     */
    Integer getBrokerSpread(ISourceDTO source, String topic);

    /**
     * 获取kafka版本
     *
     * @param host
     * @param port
     * @param id
     * @return
     */
    String getBrokerKafkaVersion(String host, Integer port, String id);

    /**
     * 获取topic 元数据
     *
     * @param source
     * @param topIcName
     * @return
     */
    List<TopIcMetaInfoDTO> getTopIcMetaByTopIc(ISourceDTO source, String topIcName);

    /**
     * broker cpu使用率
     *
     * @param source
     * @param host
     * @param port
     * @return
     */
    String getBrokerCpuUse(ISourceDTO source, String host, Integer port);

    /**
     * broker内存使用
     *
     * @param source
     * @param host
     * @param port
     * @return
     */
    String getBrokerMemoryUse(ISourceDTO source, String host, Integer port);

    /**
     * broker 内存使用率百分比
     *
     * @param source
     * @param host
     * @param port
     * @return
     */
    String getBrokerMemoryUsePercent(ISourceDTO source, String host, Integer port);

    /**
     * topic 监控
     *
     * @param source
     * @param topic
     * @return
     */
    Map<String, KafkaMonitorDTO> getTopicMonitor(ISourceDTO source, String topic);

    /**
     * 获取在线broker元数据
     *
     * @param host
     * @param port
     * @return
     */
    Map<String, KafkaMonitorDTO> getOnlineAllBrokersMBean(String host, Integer port);

    /**
     * 获取kafka集群cpu使用率
     *
     * @param source
     * @return
     */
    double getCpuUsed(ISourceDTO source);

    /**
     * 获取kafka集群内存使用率
     *
     * @param source
     * @return
     */
    double getOSMemory(ISourceDTO source);

    /**
     * 创建topic
     *
     * @param sourceDTO
     * @param kafkaTopicDTO
     */
    Boolean createTopicFromBroker(ISourceDTO sourceDTO, KafkaTopicDTO kafkaTopicDTO);

    /**
     * 创建分区
     *
     * @param sourceDTO
     * @param topicName
     * @param partitions
     */
    Boolean createTopicPartitions(ISourceDTO sourceDTO, String topicName, Integer partitions);

    /**
     * 删除topic
     *
     * @param sourceDTO
     * @param topicName
     */
    Boolean deleteTOpic(ISourceDTO sourceDTO, String topicName);

    /*
     * 获取kafka监控
     */
    List<KpiInfoDTO> getBrokerKpiInfos(ISourceDTO sourceDTO);

    long getLogSizeByTopic(ISourceDTO sourceDTO, String topic);

    long getCapacityByTopic(ISourceDTO sourceDTO, String topic);

    long getByteInByTopic(ISourceDTO sourceDTO, String topic);

    long getByteOutByTopic(ISourceDTO sourceDTO, String topic);

    List<OffsetInfoDTO> getTopicOffset(ISourceDTO sourceDTO, String topicName, String groupName);

    long getKafkaProducerLogSizeByTopic(ISourceDTO sourceDTO, String topic);

    String getConsumerStatus(ISourceDTO sourceDTO, String groupId);

    OffsetInfoDTO getTopicMessageStatistics(ISourceDTO sourceDTO, String topic, String groupName);
    JSONObject getAllActiveTopicsTree(ISourceDTO sourceDTO);
}
