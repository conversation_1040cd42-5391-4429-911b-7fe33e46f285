/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.client.mq;

import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.dto.*;
import com.dtstack.dtcenter.loader.ClassLoaderCallBackMethod;
import com.dtstack.dtcenter.loader.client.IKafka;
import com.dtstack.dtcenter.loader.dto.KafkaConsumerDTO;
import com.dtstack.dtcenter.loader.dto.KafkaOffsetDTO;
import com.dtstack.dtcenter.loader.dto.KafkaPartitionDTO;
import com.dtstack.dtcenter.loader.dto.KafkaTopicDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.KafkaSourceDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 下午2:23 2020/6/2
 * @Description：kafka代理
 */
@Slf4j
public class KafkaClientProxy<T> implements IKafka<T> {

    private IKafka targetClient;

    public KafkaClientProxy(IKafka targetClient) {
        this.targetClient = targetClient;
    }

    @Override
    public Boolean testCon(ISourceDTO source) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.testCon(source),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String getAllBrokersAddress(ISourceDTO source) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getAllBrokersAddress(source),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<BrokersDTO> getAllBrokersInfoList(ISourceDTO source) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getAllBrokersInfoList(source),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<String> getTopicList(ISourceDTO source) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getTopicList(source),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<TopIcInfoDTO> getTopicInfoList(ISourceDTO source) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getTopicInfoList(source),
                targetClient.getClass().getClassLoader());
    }


    @Override
    public List<T> getAllPartitions(ISourceDTO source, String topic) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getAllPartitions(source, topic),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<KafkaOffsetDTO> getOffset(ISourceDTO source, String topic) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getOffset(source, topic),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<List<Object>> getPreview(ISourceDTO source, SqlQueryDTO queryDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getPreview(source, queryDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<List<Object>> getPreview(ISourceDTO source, SqlQueryDTO queryDTO, String prevMode) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getPreview(source, queryDTO, prevMode),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<List<Object>> getRecordsFromKafkaByStatistics(ISourceDTO sourceDTO, Integer partition,String topic, String autoReset, String maxpoolrecords) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getRecordsFromKafkaByStatistics(sourceDTO, partition,topic, autoReset,maxpoolrecords),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<KafkaPartitionDTO> getTopicPartitions(ISourceDTO source, String topic) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getTopicPartitions(source, topic),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<String> consumeData(ISourceDTO source, String topic, Integer collectNum, String offsetReset, Long timestampOffset, Integer maxTimeWait) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.consumeData(source, topic, collectNum, offsetReset, timestampOffset, maxTimeWait),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<String> listConsumerGroup(ISourceDTO source) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.listConsumerGroup(source),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<ConsumerInfoDTO> getConsumerInfoList(ISourceDTO source) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getConsumerInfoList(source),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public JSONObject listTopicInfoByGroupId(ISourceDTO sourceDTO, String groupId) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.listTopicInfoByGroupId(sourceDTO,groupId),
                targetClient.getClass().getClassLoader());
    }


    @Override
    public List<String> listConsumerGroupByTopic(ISourceDTO source, String topic) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.listConsumerGroupByTopic(source, topic),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<KafkaConsumerDTO> getGroupInfoByGroupId(ISourceDTO source, String groupId) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getGroupInfoByGroupId(source, groupId),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<KafkaConsumerDTO> getGroupInfoByTopic(ISourceDTO source, String topic) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getGroupInfoByTopic(source, topic),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<KafkaConsumerDTO> getGroupInfoByGroupIdAndTopic(ISourceDTO source, String groupId, String topic) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getGroupInfoByGroupIdAndTopic(source, groupId, topic),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Integer getBrokerLeaderSkewed(ISourceDTO source, String topic) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getBrokerLeaderSkewed(source, topic),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Integer getBrokerSkewed(ISourceDTO source, String topic) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getBrokerSkewed(source, topic),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Integer getBrokerSpread(ISourceDTO source, String topic) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getBrokerSpread(source, topic),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String getBrokerKafkaVersion(String host, Integer port, String id) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getBrokerKafkaVersion(host,port,id),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<TopIcMetaInfoDTO> getTopIcMetaByTopIc(ISourceDTO source, String topIcName) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getTopIcMetaByTopIc(source,topIcName),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String getBrokerCpuUse(ISourceDTO source, String host, Integer port)  {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getBrokerCpuUse(source,host,port),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String getBrokerMemoryUse(ISourceDTO source, String host, Integer port) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getBrokerMemoryUse(source,host,port),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String getBrokerMemoryUsePercent(ISourceDTO source, String host, Integer port)  {

        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getBrokerMemoryUsePercent(source,host,port),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Map<String, KafkaMonitorDTO> getTopicMonitor(ISourceDTO source, String topic) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getTopicMonitor(source,topic),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Map<String, KafkaMonitorDTO>  getOnlineAllBrokersMBean(String host, Integer port) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getOnlineAllBrokersMBean(host,port),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public double getCpuUsed(ISourceDTO source) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getCpuUsed(source),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public double getOSMemory(ISourceDTO source) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getOSMemory(source),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Boolean createTopicFromBroker(ISourceDTO sourceDTO, KafkaTopicDTO kafkaTopicDTO) {
        return   ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.createTopicFromBroker(sourceDTO,kafkaTopicDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Boolean createTopicPartitions(ISourceDTO sourceDTO, String topicName, Integer partitions) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.createTopicPartitions(sourceDTO,topicName,partitions),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public Boolean deleteTOpic(ISourceDTO sourceDTO, String topicName) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.deleteTOpic(sourceDTO,topicName),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<KpiInfoDTO>  getBrokerKpiInfos(ISourceDTO sourceDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getBrokerKpiInfos(sourceDTO),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public long getLogSizeByTopic(ISourceDTO sourceDTO, String topic) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getLogSizeByTopic(sourceDTO,topic),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public long getCapacityByTopic(ISourceDTO sourceDTO, String topic) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getCapacityByTopic(sourceDTO,topic),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public long getByteInByTopic(ISourceDTO sourceDTO, String topic) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getByteInByTopic(sourceDTO,topic),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public long getByteOutByTopic(ISourceDTO sourceDTO, String topic) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getByteOutByTopic(sourceDTO,topic),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public List<OffsetInfoDTO> getTopicOffset(ISourceDTO sourceDTO, String topicName, String groupName) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getTopicOffset(sourceDTO,topicName,groupName),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public long getKafkaProducerLogSizeByTopic(ISourceDTO sourceDTO, String topic) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getKafkaProducerLogSizeByTopic(sourceDTO,topic),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public String getConsumerStatus(ISourceDTO sourceDTO, String groupId) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getConsumerStatus(sourceDTO,groupId),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public OffsetInfoDTO getTopicMessageStatistics(ISourceDTO sourceDTO, String topic, String groupName) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getTopicMessageStatistics(sourceDTO,topic,groupName),
                targetClient.getClass().getClassLoader());
    }

    @Override
    public JSONObject getAllActiveTopicsTree(ISourceDTO sourceDTO) {
        return ClassLoaderCallBackMethod.callbackAndReset(() -> targetClient.getAllActiveTopicsTree(sourceDTO),
                targetClient.getClass().getClassLoader());
    }


}
