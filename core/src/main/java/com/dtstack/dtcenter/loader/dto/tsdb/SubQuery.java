/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.dto.tsdb;

import java.util.*;

import com.alibaba.fastjson.annotation.JSONType;
import com.dtstack.dtcenter.loader.enums.Granularity;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class SubQuery {
    private int index;
    private String aggregator;
    private String metric;
    private String downsample;
    private Boolean rate;
    private RateOptions rateOptions;
    private Boolean delta;
    private DeltaOptions deltaOptions;

    private Map<String, String> tags;
    private String granularity;
    private Boolean explicitTags;
    private Integer realTimeSeconds;
    private Integer limit;
    private Integer globalLimit;
    private Integer offset;
    private String dpValue;
    private String preDpValue;
    private List<Filter> filters;
    private Map<String, Map<String, Integer>> hint;
}
