package com.dtstack.dtcenter.loader.enums;

import lombok.Getter;

import java.sql.Types;

/**
 * <AUTHOR>
 * @Date ：Created in 10:15 2021/8/16
 * @Description：自定义 数据类型
 */
public enum CommonDBDataType {

    /**
     * ---------------mogodb-------------/
     * <p>
     * /**
     * ObjectId
     */
    OBJECTID("STRING", Types.VARCHAR),

    /**
     * String
     */
    STRING("STRING", Types.VARCHAR),

    /**
     * VARCHAR
     */
    VARCHAR("VARCHAR", Types.VARCHAR),

    /**
     * Boolean
     */
    BOOLEAN("BOOLEAN", Types.BOOLEAN),

    /**
     * Binary
     */
    BINARY("BINARY", Types.BINARY),

    /**
     * Int32
     */
    INT32("INTEGER", Types.INTEGER),

    /**
     * Int
     */
    INT("INT", Types.INTEGER),
    /**
     * Int64
     */
    INT64("BIGINT", Types.BIGINT),

    /**
     * Double
     */
    DOUBLE("DOUBLE", Types.DOUBLE),

    /**
     * Decimal128
     */
    DECIMAL128("DECIMAL", Types.DECIMAL),
    /**
     * Date
     */
    DATE("DATE", Types.DATE),
    /**
     * Timestamp
     */
    TIMESTAMP("TIMESTAMP", Types.TIMESTAMP),
    /**
     * Object
     */
    OBJECT("ROW", Types.ROWID),
    /**
     * Array
     */
    ARRAY("ARRAY", Types.ARRAY),

    /**
     * INTEGER
     */
    INTEGER("INTEGER", Types.INTEGER),
    /**
     * INT8
     */
    INT8("INTEGER", Types.INTEGER),
    /**
     * INT16
     */
    INT16("INTEGER", Types.INTEGER),

    /**
     * Decimal
     */
    DECIMAL("DECIMAL", Types.DECIMAL),

    /**
     * FLOAT
     */
    FLOAT("FLOAT", Types.FLOAT),

    BIGINT("BIGINT", Types.INTEGER),

    SMALLINT("SMALLINT", Types.INTEGER),

    TINYINT("TINYINT", Types.INTEGER),
    TEXT("TEXT", Types.CLOB),
    KEYWORD("KEYWORD", Types.VARCHAR),
    LONG("LONG", Types.BIGINT),

    /**
     * UNIXTIME_MICROS
     */
    UNIXTIME_MICROS("TIMESTAMP", Types.TIMESTAMP);

    @Getter
    private String columnDataType;


    @Getter
    private Integer dataType;

    CommonDBDataType(String columnDataType, Integer dataType) {
        this.columnDataType = columnDataType;
        this.dataType = dataType;
    }

    public static CommonDBDataType getDataType(String name) {
        return CommonDBDataType.valueOf(name);
    }
}
