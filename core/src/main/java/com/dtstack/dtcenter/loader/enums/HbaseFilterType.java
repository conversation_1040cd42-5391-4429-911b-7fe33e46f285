/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.enums;

/**
 * -
 *
 * <AUTHOR>
 * date：Created in 上午10:52 2020/8/25
 * company: www.dtstack.com
 */
public enum HbaseFilterType {

    PAGE_FILTER(1),

    SINGLE_COLUMN_VALUE_FILTER(2),

    ROW_FILTER(3),

    TIMESTAMP_FILTER(4),

    FILTER_LIST(5);

    private final Integer val;

    HbaseFilterType(Integer val) {
        this.val = val;
    }

    public Integer getVal() {
        return val;
    }
}
