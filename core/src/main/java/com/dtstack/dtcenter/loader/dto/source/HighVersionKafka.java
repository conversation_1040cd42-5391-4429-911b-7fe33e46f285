package com.dtstack.dtcenter.loader.dto.source;

import com.dtstack.dtcenter.loader.source.DataSourceType;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * @描述
 * @创建人 lf
 * @创建时间 2022/10/18
 * @create 2022-10-18-11:02
 */
@Data
@ToString
@SuperBuilder
public class HighVersionKafka extends KafkaSourceDTO {

    @Override
    public Integer getSourceType() {
        return DataSourceType.HIGH_VERSION_KAFKA.getVal();
    }
}
