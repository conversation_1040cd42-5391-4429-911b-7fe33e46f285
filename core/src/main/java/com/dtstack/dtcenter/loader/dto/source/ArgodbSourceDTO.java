package com.dtstack.dtcenter.loader.dto.source;

import com.dtstack.dtcenter.loader.source.DataSourceType;
import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2024/5/7 10:57
 */
@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ArgodbSourceDTO extends RdbmsSourceDTO {
    /**
     * Hadoop defaultFS
     */
    @Builder.Default
    private String defaultFS = "";

    /**
     * Hadoop/ Hbase 配置信息
     */
    private String config;

    /**
     * hive metaStore 连接地址
     */
    private String metaStoreUris;

    @Override
    public Integer getSourceType() {
        return DataSourceType.ArgoDB.getVal();
    }
}
