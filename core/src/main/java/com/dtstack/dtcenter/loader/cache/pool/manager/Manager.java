/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.cache.pool.manager;

import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 下午3:27 2020/8/3
 * @Description：自定义连接池管理接口
 */
public interface Manager<T> {

    /**
     * 获取连接
     *
     * @param source
     * @return
     * @throws Exception
     */
    T getConnection(ISourceDTO source) throws Exception;

    /**
     * 初始化数据源连接池
     *
     * @param source
     * @return
     */
    T initSource(ISourceDTO source);
}
