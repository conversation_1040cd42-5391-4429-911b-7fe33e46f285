/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.source;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 10:39 2020/7/27
 * @Description：数据源基础
 */
public enum DataBaseType {
    MySql("mysql", "com.mysql.jdbc.Driver"),
    StarRocks("mysql", "com.mysql.jdbc.Driver"),
    TDDL("mysql", "com.mysql.jdbc.Driver"),
    DRDS("drds", "com.mysql.jdbc.Driver"),
    Oracle("oracle", "oracle.jdbc.OracleDriver"),
    SQLServer("sqlserver", "net.sourceforge.jtds.jdbc.Driver", "select 1111"),
    SQLSSERVER_2017_LATER("sqlserver_2017_later", "com.microsoft.sqlserver.jdbc.SQLServerDriver", "select 1111"),
    PostgreSQL("postgresql", "org.postgresql.Driver"),
    TDSQL_FOR_PG("TDSQL_FOR_PG", "org.postgresql.Driver"),
    TDSQL_FOR_ORACLE("TDSQL_FOR_ORACLE", "org.postgresql.Driver"),
    TBase("TBase", "org.postgresql.Driver"),
    DWS_PG("DWS_PG", "org.postgresql.Driver"),
    DWS_MySQL("DWS_MySQL", "com.mysql.jdbc.Driver"),
    RDBMS("rdbms", "com.alibaba.rdbms.plugin.rdbms.util.DataBaseType"),
    DB2("db2", "com.ibm.db2.jcc.DB2Driver"),
    DB2_AS400("db2_as400", "com.ibm.as400.access.AS400JDBCDriver","SELECT 1 FROM SYSIBM.SYSDUMMY1"),
    HIVE("hive", "org.apache.hive.jdbc.HiveDriver"),
    HIVE3("hive3", "org.apache.hive.jdbc.HiveDriver"),
    HIVE3_CDP("hive3_cdp", "org.apache.hive.jdbc.HiveDriver"),
    HIVE3_MRS("hive3_mrs", "org.apache.hive.jdbc.HiveDriver"),
    CarbonData("carbonData", "org.apache.hive.jdbc.HiveDriver"),
    Spark("hive", "org.apache.hive.jdbc.HiveDriver"),
    INCEPTOR("inceptor", "org.apache.hive.jdbc.HiveDriver"),
    INCEPTOR8("inceptor8", "org.apache.hive.jdbc.HiveDriver"),
    ADS("mysql", "com.mysql.jdbc.Driver"),
    ADB_FOR_PG("postgresql", "org.postgresql.Driver"),
    GP_FOR_PG("postgresql", "org.postgresql.Driver"),
    RDS("mysql", "com.mysql.jdbc.Driver"),
    MaxCompute("maxcompute", "com.aliyun.odps.jdbc.OdpsDriver"),
    LIBRA("postgresql", "org.postgresql.Driver"),
    GBase8a("gbase", "com.gbase.jdbc.Driver", "select 1111"),
    Kylin("kylin", "org.apache.kylin.jdbc.Driver", "select 1111"),
    Kudu("kudu", "org.apache.hive.jdbc.HiveDriver"),
    Impala("impala", "com.cloudera.impala.jdbc41.Driver"),
    Clickhouse("clickhouse", "ru.yandex.clickhouse.ClickHouseDriver"),
    Clickhouse_Mrs("clickhouse", "com.clickhouse.jdbc.ClickHouseDriver"),
    HIVE1X("hive1", "org.apache.hive.jdbc.HiveDriver", "show tables"),
    Polardb_For_MySQL("mysql", "com.mysql.jdbc.Driver", "!table"),
    Phoenix("Phoenix", "org.apache.phoenix.jdbc.PhoenixDriver"),
    TiDB("TiDB", "com.mysql.jdbc.Driver"),
    MySql8("mysql8", "com.mysql.cj.jdbc.Driver"),
    DMDB("DMDB For MySQL", "dm.jdbc.driver.DmDriver"),
    DMDB_For_Oracle("DMDB For Oracle", "dm.jdbc.driver.DmDriver"),
    Greenplum6("Greenplum6", "com.pivotal.jdbc.GreenplumDriver"),
    Phoenix5("Phoenix5", "org.apache.phoenix.jdbc.PhoenixDriver"),
    KINGBASE8("kingbase8", "com.kingbase8.Driver"),
    Presto("presto", "com.facebook.presto.jdbc.PrestoDriver", "select 1111"),
    TRINO("trino", "io.trino.jdbc.TrinoDriver", "select 1111"),
    OceanBase("oceanbase", "com.alipay.oceanbase.jdbc.Driver", "select 1111"),
    Doris("doris", "com.mysql.jdbc.Driver", "select 1111"),
    sapHana1("sapHana1", "com.sap.db.jdbc.Driver", "select 1111"),
    sapHana2("sapHana2", "com.sap.db.jdbc.Driver", "select 1111"),
    MariaDB("MariaDB", "com.mysql.jdbc.Driver"),
    GaussDB("GaussDB", "org.postgresql.Driver"),
    Gauss_DB200("Gauss_DB200", "com.huawei.gauss200.jdbc.Driver"),
    GaussDB_FOR_MySQL("GaussDB_FOR_MySQL", "com.mysql.jdbc.Driver"),
    DDM_FOR_MYSQL("ddm_for_mysql", "com.mysql.jdbc.Driver"),
    ArgoDB("argodb", "org.apache.hive.jdbc.HiveDriver"),
//    GoldenDB("GoldenDB", "com.mysql.jdbc.Driver"),
    GoldenDB("GoldenDB", "com.goldendb.jdbc.Driver"),

    Informix("Informix", "com.informix.jdbc.IfxDriver"),
    KunDB("KunDB", "com.trino.jdbc.TrinoDriver"),
    Sequoiadb_FOR_MYSQL("Sequoiadb", "com.mysql.jdbc.Driver"),
    Sybase_jTDS("Sybase_jTDS", "net.sourceforge.jtds.jdbc.Driver", "select 1111"),
    Sybase_jConnect("Sybase_jConnect", "com.sybase.jdbc4.jdbc.SybDriver", "select 1111"),;
    private String typeName;
    private String driverClassName;
    private String testSql;

    DataBaseType(String typeName, String driverClassName) {
        this.typeName = typeName;
        this.driverClassName = driverClassName;
    }

    DataBaseType(String typeName, String driverClassName, String testSql) {
        this.typeName = typeName;
        this.driverClassName = driverClassName;
        this.testSql = testSql;
    }

    public String getDriverClassName() {
        return this.driverClassName;
    }

    public String getTestSql() {
        return testSql;
    }

    public String getTypeName() {
        return typeName;
    }
}
