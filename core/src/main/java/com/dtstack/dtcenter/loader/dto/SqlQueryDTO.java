/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.dto;

import com.dsg.database.datasource.dto.DatasourceInfoImportVO;
import com.dtstack.dtcenter.loader.dto.filter.Filter;
import com.dtstack.dtcenter.loader.enums.EsCommandType;
import com.dtstack.dtcenter.loader.enums.MatchType;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.ArrayUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 14:40 2020/2/26
 * @Description：查询信息
 */
@Data
@Builder
public class SqlQueryDTO {
    /**
     * 查询 SQL
     */
    private String sql;

    /**
     * schema/db
     */
    private String schema;


    /**
     * schema/db
     */
    private String dbName;

    /**
     * 表名称
     */
    private String tableName;

    /**
     * 表名称搜索
     */
    private String tableNamePattern;

    /**
     * 表名称匹配类型
     */
    private MatchType matchType;

    /**
     * 表类型 部分支持，建议只使用 view 这个字段
     * {@link java.sql.DatabaseMetaData#getTableTypes()}
     */
    private String[] tableTypes;

    /**
     * 字段名称
     */
    private List<String> columns;

    /**
     * 分区字段:值,用于hive分区预览数据、hbase自定义查询
     */
    private Map<String, String> partitionColumns;

    /**
     * 是否需要视图表，默认 false 不过滤
     */
    private Boolean view;

    /**
     * 是否过滤分区字段，默认 false 不过滤
     */
    private Boolean filterPartitionColumns;

    /**
     * 预览条数，默认100
     */
    private Integer previewNum;

    /**
     * 当前页
     */
    private Integer pageNum;

    /**
     * 预编译字段
     * todo 修改executeQuery方法为支持预编译
     * time :2020-08-04 15:49:00
     */
    private List<Object> preFields;

    /**
     * executorQuery查询超时时间,单位：秒
     */
    private Integer queryTimeout;

    /**
     * mongodb，executorQuery 分页查询，开始行
     */
    private Integer startRow;

    /**
     * mongodb，executorQuery 分页查询，限制条数
     */
    private Integer limit;

    /**
     * 是否设置 maxRow，默认 true
     */
    @Builder.Default
    private Boolean setMaxRow = true;

    /**
     * hbase过滤器，用户hbase自定义查询
     */
    private List<Filter> hbaseFilter;

    /**
     * JDBC 每次读取数据的行数，使用 DBUtil.setFetchSize()
     */
    private Integer fetchSize;

    /**
     * SQL where拼接关联关系
     */
    private String whereRelation;

    /**
     * SQL where拼接条件
     */
    private List<RelationConditionDTO> whereConditions;

    /**
     * Impala数据源预览数据需要order by的字段（如果是查询分页数据必填！！！）
     */
    private String impalaOrderByColumn;

    /**
     * solr 自定义查询
     */
    private SolrQueryDTO solrQueryDTO;

    /**
     * 字段类型
     */
    private String columnType;

    /**
     * 数据源导入对象
     */
    private DatasourceInfoImportVO datasourceInfoImportVO;

    /**
     * 业务系统
     */
    private String businessUuid;

    /**
     * 存储过程名称
     */
    private String objectName;

    /**
     * 数据源id
     */
    private String datasourceId;
    /**
     * 类型
     */
    private String type;

    /**
     * Elasticsearch 命令, 定义es操作类型
     * <b><b/>
     * <ul>
     *     <li>INSERT(0) insert 操作，插入时要指定_id</li>
     *     <li>UPDATE(1) _update 操作，指定_id</li>
     *     <li>DELETE(2) delete操作，删除单条数据要指定_id</li>
     *     <li>BULK(3) _bulk批量操作，默认请求/_bulk</li>
     * <ul/>
     * 默认执行POST请求，请求参数中的tableName作为esclient的endpoint
     * <br>
     * refer to {@link EsCommandType}
     */
    private Integer esCommandType;

    /**
     * 是否忽略大小写
     * true 忽略大小写
     * false 不忽略大小写
     */
    private Boolean isCapitalization;

    public Boolean getView() {
        if (ArrayUtils.isEmpty(getTableTypes())) {
            return Boolean.TRUE.equals(view);
        }

        return Arrays.stream(getTableTypes()).filter(type -> "VIEW" .equalsIgnoreCase(type)).findFirst().isPresent();
    }

    public Integer getPreviewNum() {
        if (this.previewNum == null) {
            return 100;
        }
        return previewNum;
    }

    public Boolean getFilterPartitionColumns() {
        return Boolean.TRUE.equals(filterPartitionColumns);
    }


}