/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader;

import java.util.List;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 下午7:42 2020/6/2
 * @Description：
 */
public interface IDownloader {

    /**
     * 配置下载器
     *
     * @return
     * @throws Exception
     */
    boolean configure() throws Exception;

    /**
     * 获取元数据信息
     *
     * @return
     * @throws Exception
     */
    List<String> getMetaInfo();

    /**
     * 读取下一行
     *
     * @return
     * @throws Exception
     */
    Object readNext();

    /**
     * 是否末行
     *
     * @return
     * @throws Exception
     */
    boolean reachedEnd();

    /**
     * 是否关闭
     *
     * @return
     * @throws Exception
     */
    boolean close() throws Exception;

    /**
     * 获取文件名
     *
     * @return
     * @throws Exception
     */
    String getFileName();

    /**
     * 获取容器
     *
     * @return
     * @throws Exception
     */
    List<String> getContainers();
}
