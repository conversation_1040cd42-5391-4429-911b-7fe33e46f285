package com.dtstack.dtcenter.loader.enums;

import lombok.Getter;

import java.sql.Types;

/**
 * <AUTHOR>
 * @Date ：Created in 10:15 2021/8/16
 * @Description：hive 数据类型
 */
public enum HiveDBDataType {

    /**
     * ---------------mogodb-------------/
     * <p>
     * /**
     * BOOLEAN
     */
    BOOLEAN("BOOLEAN", Types.BOOLEAN),

    /**
     * TINYINT
     */
    TINYINT("TINYINT", Types.TINYINT),
    /**
     * TINYINT
     */
    SMALLINT("SMALLINT", Types.SMALLINT),

    /**
     * Int32
     */
    INT("INTEGER", Types.INTEGER),
    /**
     * Int64
     */
    INTEGER("INTEGER", Types.INTEGER),

    /**
     * INTEGER
     */
    BIGINT("INTEGER", Types.BIGINT),

    /**
     * FLOAT
     */
    FLOAT("FLOAT", Types.FLOAT),

    /**
     * Double
     */
    DOUBLE("Double", Types.DOUBLE),


    /**
     * CHAR
     */
    CHAR("String", Types.VARCHAR),

    /**
     * CHAR
     */
    VARCHAR("String", Types.VARCHAR),

    /**
     * CHAR
     */
    STRING("String", Types.VARCHAR),
    /**
     * Date
     */
    DATE("Date", Types.DATE),

    /**
     * DATETIME
     */
    DATETIME("Timestamp", Types.TIMESTAMP),


    /**
     * Timestamp
     */
    TIMESTAMP("Timestamp", Types.TIMESTAMP),

    /**
     * Binary
     */
    BINARY("String", Types.VARCHAR),

    /**
     * Array
     */
    ARRAY("String", Types.VARCHAR),

    /**
     * Object
     */
    INTERVAL("String", Types.VARCHAR),

    /**
     * Object
     */
    MAP("String", Types.VARCHAR),

    /**
     * Object
     */
    STRUCT("String", Types.VARCHAR),

    /**
     * Object
     */
    UNIONTYPE("String", Types.VARCHAR),
    ;

    @Getter
    private String columnDataType;


    @Getter
    private Integer dataType;

    HiveDBDataType(String columnDataType, Integer dataType) {
        this.columnDataType = columnDataType;
        this.dataType = dataType;
    }

    public static HiveDBDataType getDataType(String name) {
        return HiveDBDataType.valueOf(name);
    }
}
