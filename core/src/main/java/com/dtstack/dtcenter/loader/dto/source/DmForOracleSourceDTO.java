package com.dtstack.dtcenter.loader.dto.source;

import com.dtstack.dtcenter.loader.source.DataSourceType;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 17:23 2020/5/22
 * @Description：达梦数据源信息
 */
@Data
@ToString
@SuperBuilder
public class DmForOracleSourceDTO extends DmSourceDTO {

    @Override
    public Integer getSourceType() {
        return DataSourceType.DMDB_For_Oracle.getVal();
    }
}
