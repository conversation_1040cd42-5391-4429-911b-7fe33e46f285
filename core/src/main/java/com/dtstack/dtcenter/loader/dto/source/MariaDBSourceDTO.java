package com.dtstack.dtcenter.loader.dto.source;

import com.dtstack.dtcenter.loader.source.DataSourceType;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @create 2022-08-17-18:03
 * @description:
 */
@Data
@ToString
@SuperBuilder
public class MariaDBSourceDTO extends RdbmsSourceDTO {

    @Override
    public Integer getSourceType() {
        return DataSourceType.MariaDB.getVal();
    }

}
