/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.client;

import com.alibaba.fastjson.JSONObject;
import com.dtstack.dtcenter.loader.dto.HbaseQueryDTO;
import com.dtstack.dtcenter.loader.dto.filter.TimestampFilter;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;

import java.util.List;
import java.util.Map;

/**
 * hbase客户端接口
 *
 * <AUTHOR>
 * date：Created in 9:38 上午 2020/12/2
 * company: www.dtstack.com
 */
public interface IHbase {

    /**
     * 判断namespace是否存在
     *
     * @param source    数据源信息
     * @param namespace hbase namespace
     * @return 是否存在
     */
    Boolean isDbExists(ISourceDTO source, String namespace);

    /**
     * hbase创建表
     *
     * @param source    数据源信息
     * @param tableName 表名
     * @param colFamily 列族列表
     * @return 创建结果
     */
    Boolean createHbaseTable(ISourceDTO source, String tableName, String[] colFamily);

    /**
     * hbase创建表，指定namespace
     *
     * @param source    数据源信息
     * @param namespace hbase namespace
     * @param tableName 表名
     * @param colFamily 列族列表
     * @return 创建结果
     */
    Boolean createHbaseTable(ISourceDTO source, String namespace, String tableName, String[] colFamily);

    /**
     * hbase 删除表
     *
     * @param source    数据源信息
     * @param tableName 表名
     */
    Boolean deleteHbaseTable(ISourceDTO source, String tableName);

    /**
     * hbase 删除表
     *
     * @param source    数据源信息
     * @param tableName 表名
     */
    Boolean deleteHbaseTable(ISourceDTO source, String namespace, String tableName);

    /**
     * hbase 根据正则查询匹配的数据，返回rowkey集合
     *
     * @param source    数据源连接信息
     * @param tableName 表名
     * @param regex     匹配正则
     * @return 查询结果
     */
    List<String> scanByRegex(ISourceDTO source, String tableName, String regex);

    /**
     * hbase 根据rowKey删除数据
     *
     * @param source    数据源信息
     * @param tableName 表名
     * @param rowKeys   rowkey列表
     * @return 删除状态
     */
    Boolean deleteByRowKey(ISourceDTO source, String tableName, String family, String qualifier, List<String> rowKeys);

    /**
     * hbase向指定的rowKey插入数据
     *
     * @param source    数据源信息
     * @param tableName 表名
     * @param rowKey    hbase rowkey
     * @param family    列族
     * @param qualifier 列名
     * @param data      数据
     * @return 数据插入状态
     */
    Boolean putRow(ISourceDTO source, String tableName, String rowKey, String family, String qualifier, String data);

    /**
     * hbase根据rowKey获取数据
     *
     * @param source    数据源信息
     * @param tableName 表名
     * @param rowKey    hbase rowkey
     * @return 查询结果
     */
    String getRow(ISourceDTO source, String tableName, String rowKey, String family, String qualifier);

    /**
     * hbase 数据预览，如果有数据则第一行为字段信息，如果没有数据返回空 list
     *
     * @param source     数据源信息
     * @param tableName  表名
     * @param previewNum 预览条数，最大5000条 默认100条
     * @return 预览数据
     */
    List<List<String>> preview(ISourceDTO source, String tableName, Integer previewNum);

    /**
     * hbase 数据预览，如果有数据则第一行为字段信息，如果没有数据返回空 list
     *
     * @param source     数据源信息
     * @param tableName  表名
     * @param familyList 预览指定列族的数据，即当前行如果有满足该列族的数据就返回
     * @param previewNum 预览条数，最大5000条 默认100条
     * @return 预览数据
     */
    List<List<String>> preview(ISourceDTO source, String tableName, List<String> familyList, Integer previewNum);

    /**
     * hbase 数据预览，如果有数据则第一行为字段信息，如果没有数据返回空 list
     *
     * @param source             数据源信息
     * @param tableName          表名
     * @param familyQualifierMap 预览指定列族、列名下的数据
     * @param previewNum         预览条数，最大5000条 默认100条
     * @return 预览数据
     */
    List<List<String>> preview(ISourceDTO source, String tableName, Map<String, List<String>> familyQualifierMap, Integer previewNum);

    /**
     * hbase 自定义查询
     *
     * @param source          数据源信息
     * @param hbaseQueryDTO   hbase 查询条件
     * @param timestampFilter 时间戳过滤
     * @return 查询结果
     */
    List<Map<String, Object>> executeQuery(ISourceDTO source, HbaseQueryDTO hbaseQueryDTO, TimestampFilter timestampFilter);


    //hbase相关操作
    //获取所有的命名空间
    List<JSONObject> getAllNameSpaces(ISourceDTO iSource);

    List<JSONObject> getNameSpacesByName(ISourceDTO iSource, String namespaceName);

    //创建命名空间
    Boolean createNameSpaces(ISourceDTO iSource, List<JSONObject> namespaceDescriptors);

    //获取所有的表信息
    List<Object> getAllTableDescriptor(ISourceDTO iSource);

    //获取指定表的表信息
    List<Object> getTableDescriptorByTableName(ISourceDTO iSource, String tableName) throws Exception;

    //判断表是否存在
    Boolean tableExist(ISourceDTO iSource, String tableName) throws Exception;

    //根据表信息创建表
    Boolean createTableDescriptor(ISourceDTO iSource, List<Object> tableDescriptors) throws Exception;

    //创建快照
    Boolean createTableSnapshot(ISourceDTO iSource, String tableName, String snapshotName) throws Exception;

    //导出快照
    String exportSnapshot(ISourceDTO iSource, String snapshotName, String hbaseExportPath, String resourceJarPath, String callBackUrl, String logOutPutPath) throws Exception;

    //恢复到快照
    Boolean doRestoreHbase(ISourceDTO iSource, String tabName, String snapshotName) throws Exception;

    //删除快照
    Boolean deleteHbaseSnap(ISourceDTO iSource, String snapshotName) throws Exception;

    //导出数据
    String exportData(ISourceDTO iSource, String tableName, String hbaseExportPath, String resourceJarPath, String startTime, String endTime, String callBackUrl, String logOutPutPath) throws Exception;

    //导入数据
    String importData(ISourceDTO iSource, String tableName, String hbaseImportPath, String resourceJarPath, String callBackUrl, String logOutPutPath) throws Exception;

}
