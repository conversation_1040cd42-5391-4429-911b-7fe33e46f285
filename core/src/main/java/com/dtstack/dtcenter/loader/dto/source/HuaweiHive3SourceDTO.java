package com.dtstack.dtcenter.loader.dto.source;

import com.dtstack.dtcenter.loader.source.DataSourceType;
import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2024/4/2 14:05
 */
@Data
@ToString
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class HuaweiHive3SourceDTO extends RdbmsSourceDTO {
    /**
     * Hadoop defaultFS
     */
    @Builder.Default
    private String defaultFS = "";

    /**
     * Hadoop/ Hbase 配置信息
     */
    private String config;

    /**
     * hive ssl
     */
    private HiveSslConfig hiveSslConfig;


    /**
     * 认证用户
     */
    private String authUser;

    @Override
    public Integer getSourceType() {
        return DataSourceType.HIVE3X_MRS.getVal();
    }
}
