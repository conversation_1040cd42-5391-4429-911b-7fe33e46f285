package com.dsg.database.datasource.utils;

import lombok.extern.slf4j.Slf4j;

import javax.security.auth.login.AppConfigurationEntry;
import javax.security.auth.login.Configuration;

/**
 * 清理环境变量工具类
 *
 *
 * <AUTHOR>
 * @date 2022/7/11 14:31
 */
@Slf4j
public class CleanSystemPropertiesUtils {

    private static final String JAVA_SECURITY_KRB5_CONF_KEY = "java.security.krb5.conf";
    private static final String JAVA_SECURITY_LOGIN_CONF_KEY = "java.security.auth.login.config";
    private static final String JAVA_SECURITY_AUTH_USESUBJECTCREDSONLY = "javax.security.auth.useSubjectCredsOnly";
    private static final String ZOOKEEPER_SERVER_PRINCIPAL_KEY = "zookeeper.server.principal";
    private static final String ZOOKEEPER_CLIENTCNXNSOCKET = "zookeeper.clientCnxnSocket";
    private static final String ZOOKEEPER_CLIENT_SECURE = "zookeeper.client.secure";

    private static final String ZOOKEEPER_DEFAULT_LOGIN_CONTEXT_NAME = "Client";




    /*
        清理hive环境变量
     */
    public static void cleanHive(){
        System.clearProperty(JAVA_SECURITY_KRB5_CONF_KEY);
        System.clearProperty(ZOOKEEPER_SERVER_PRINCIPAL_KEY);
        System.clearProperty(ZOOKEEPER_CLIENTCNXNSOCKET);
        System.clearProperty(ZOOKEEPER_CLIENT_SECURE);
        log.info("======cleanHive环境变量值：{}，{}，{}，{}",JAVA_SECURITY_KRB5_CONF_KEY,ZOOKEEPER_SERVER_PRINCIPAL_KEY,ZOOKEEPER_CLIENTCNXNSOCKET,ZOOKEEPER_CLIENT_SECURE);
    }

    /*
       清理hive环境变量
    */
    public static void cleanZk(){
        System.clearProperty(ZOOKEEPER_SERVER_PRINCIPAL_KEY);
        log.error("After clearing:{},{} ",ZOOKEEPER_SERVER_PRINCIPAL_KEY, System.getProperty(ZOOKEEPER_SERVER_PRINCIPAL_KEY));
        System.clearProperty(ZOOKEEPER_CLIENTCNXNSOCKET);
        log.error("After clearing:{},{} ",ZOOKEEPER_CLIENTCNXNSOCKET, System.getProperty(ZOOKEEPER_CLIENTCNXNSOCKET));
        System.clearProperty(ZOOKEEPER_CLIENT_SECURE);
        log.error("After clearing:{},{} ",ZOOKEEPER_CLIENT_SECURE, System.getProperty(ZOOKEEPER_CLIENT_SECURE));

        System.clearProperty(ZOOKEEPER_DEFAULT_LOGIN_CONTEXT_NAME);
        log.error("After clearing:{},{} ",ZOOKEEPER_DEFAULT_LOGIN_CONTEXT_NAME, System.getProperty(ZOOKEEPER_DEFAULT_LOGIN_CONTEXT_NAME));

//        try{
//            Runtime.getRuntime().exec("kdestroy");
//            log.error("强制执行清除sasl缓存命令：{}","kdestroy");
//        }catch (Exception e){
//            log.error("清除sasl缓存失败：{}",e.getMessage());
//        }

        log.info("======cleanZk环境变量值：{}，{}，{}",ZOOKEEPER_SERVER_PRINCIPAL_KEY
                ,ZOOKEEPER_CLIENTCNXNSOCKET,ZOOKEEPER_CLIENT_SECURE);
    }

    /*
      清理hive环境变量
   */
    public static void cleanJava(){
        System.clearProperty(JAVA_SECURITY_KRB5_CONF_KEY);
        log.error("After clearing:{},{} ",JAVA_SECURITY_KRB5_CONF_KEY, System.getProperty(JAVA_SECURITY_KRB5_CONF_KEY));

        System.clearProperty(JAVA_SECURITY_AUTH_USESUBJECTCREDSONLY);
        log.error("After clearing:{},{} ",JAVA_SECURITY_AUTH_USESUBJECTCREDSONLY, System.getProperty(JAVA_SECURITY_AUTH_USESUBJECTCREDSONLY));

        System.clearProperty(JAVA_SECURITY_LOGIN_CONF_KEY);
        log.error("After clearing:{},{} ",JAVA_SECURITY_LOGIN_CONF_KEY, System.getProperty(JAVA_SECURITY_LOGIN_CONF_KEY));


       //清除sasl认证配置
        Configuration emptyConfig = new Configuration() {
            @Override
            public AppConfigurationEntry[] getAppConfigurationEntry(String name) {
                return null;
            }
        };

        javax.security.auth.login.Configuration.setConfiguration(emptyConfig);
        log.error("=====================javax.security.auth.login.Configuration.setConfiguration,{}",emptyConfig);
        log.info("======cleanJava环境变量值：{}，{}，{}",JAVA_SECURITY_KRB5_CONF_KEY,JAVA_SECURITY_KRB5_CONF_KEY,JAVA_SECURITY_LOGIN_CONF_KEY);
    }


    /*
      清理hive环境变量
   */
    public static void cleanHbase(){
        System.clearProperty(JAVA_SECURITY_KRB5_CONF_KEY);
        System.clearProperty(JAVA_SECURITY_LOGIN_CONF_KEY);
        System.clearProperty(ZOOKEEPER_SERVER_PRINCIPAL_KEY);
        System.clearProperty(ZOOKEEPER_CLIENT_SECURE);

        log.info("======清除cleanHbase环境变量值：{}，{}，{}，{}",JAVA_SECURITY_KRB5_CONF_KEY,JAVA_SECURITY_LOGIN_CONF_KEY,
                ZOOKEEPER_SERVER_PRINCIPAL_KEY,ZOOKEEPER_CLIENT_SECURE);
    }

    /*
  清理hive环境变量
*/
    public static void cleanHdfs(){
        System.clearProperty(JAVA_SECURITY_KRB5_CONF_KEY);
        System.clearProperty(JAVA_SECURITY_LOGIN_CONF_KEY);
        System.clearProperty(ZOOKEEPER_SERVER_PRINCIPAL_KEY);
        log.info("======清除cleanHdfs环境变量值：{}，{}，{}",JAVA_SECURITY_KRB5_CONF_KEY,JAVA_SECURITY_LOGIN_CONF_KEY,ZOOKEEPER_SERVER_PRINCIPAL_KEY);
    }

    /*
清理hive环境变量
*/
    public static void cleanKafka(){
        System.clearProperty(JAVA_SECURITY_LOGIN_CONF_KEY);
        System.clearProperty(JAVA_SECURITY_AUTH_USESUBJECTCREDSONLY);
        log.info("======清除cleanKafka环境变量值：{}，{}",JAVA_SECURITY_KRB5_CONF_KEY,JAVA_SECURITY_AUTH_USESUBJECTCREDSONLY);
    }
}
