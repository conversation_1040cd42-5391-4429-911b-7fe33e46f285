package com.dsg.database.datasource.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2022/7/12
 */
public class CreateTableSqlParseUtil {

    private static final String CREATE_REGEX = "(?i)create\\s+(.*\\s+)*table\\s+(if\\s+not\\s+exists\\s+)*(?<tableName>[\\.`'\"a-zA-Z0-9_]+)\\s*.*";

    private static final Pattern CREATE_PATTERN = Pattern.compile(CREATE_REGEX);

    public static String parseTableName(String originSql) {
        String sql = originSql.replace("\n", "").replace("\r", "");
        if (sql.contains("(")) {
            sql = sql.substring(0, sql.indexOf("("));
        }

        if (!sql.matches(CREATE_REGEX)) {
            throw new RuntimeException("Only accept sql like 'create table ....'");
        }

        String tableName = null;
        Matcher matcher = CREATE_PATTERN.matcher(sql);
        if (matcher.find()) {
            tableName = matcher.group("tableName");
        }

        if (tableName == null) {
            throw new RuntimeException("Can not parse tableName from sql:" + sql);
        }

        if (tableName.contains(".")) {
            tableName = tableName.split("\\.")[1];
        }

        if (tableName.contains("`") || tableName.contains("'") || tableName.contains("\"")) {
            tableName = tableName.substring(1, tableName.length() - 1);
        }

        return tableName;
    }
}
