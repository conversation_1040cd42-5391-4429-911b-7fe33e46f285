package com.dsg.database.datasource.utils;


import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.TreeMap;


public class SignatureUtils {

    /**
     * 生成签名
     *
     * @param params    参数
     * @param secretKey 密钥
     * @return 签名字符串
     */
    public static String generateSignature(Map<String, String> params, String secretKey) {
        // 1. 对参数按照key做升序排序
        TreeMap<String, String> sortedParams = new TreeMap<>(params);

        // 2. 拼接字符串
        StringBuilder stringBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            if (StringUtils.isNotEmpty(entry.getValue())) {
                stringBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
        }

        // 3. 添加密钥
        stringBuilder.append("key=").append(secretKey);

        // 4. 打印待签名字符串
        String signStr = stringBuilder.toString();

        // 5. MD5加密
        String signature = DigestUtils.md5Hex(signStr).toUpperCase();

        return signature;
    }

} 