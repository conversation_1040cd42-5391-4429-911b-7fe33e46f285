package com.dsg.database.datasource.utils;

import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.alibaba.fastjson.TypeReference;
import com.dsg.database.datasource.constants.FormNames;
import com.dsg.database.datasource.constants.MetadataConstants;
import com.dsg.database.datasource.dto.*;
import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import com.dsg.database.datasource.enums.SourceDTOType;
import com.dsg.database.datasource.exception.DatasourceDefException;
import com.dsg.database.datasource.exception.ErrorCode;
import com.dsg.database.datasource.exception.RdosDefineException;
import com.dsg.database.datasource.thread.RdosThreadFactory;
import com.dtstack.dtcenter.loader.client.*;
import com.dtstack.dtcenter.loader.dto.*;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.RdbmsSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.kerberos.HadoopConfTool;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import com.dtstack.dtcenter.loader.utils.DBUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpEntity;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.*;
import java.sql.Connection;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.dsg.database.datasource.exception.ErrorCode.CAN_NOT_FIND_DATA_SOURCE;

//import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 执行DDL、DML、DQL相关的工具类getOriginKerberosConfig
 * 其他模块引入该模块直接调用该静态类即可完成相关操作
 *
 * <AUTHOR>
 * @date 2022/7/11 14:31
 */
@Slf4j
public class DatasourceUtils {
    private static final String IS_OPEN_CDB = "select * from v$database";
    public static final String JDBC_URL = "jdbcUrl";
    public static final String JDBC_USERNAME = "username";
    public static final String JDBC_PASSWORD = "password";
    public static final String JDBC_HOSTPORTS = "hostPorts";

    public static final String HDFS_DEFAULTFS = "defaultFS";

    public static final String HADOOP_CONFIG = "hadoopConfig";

    public static String HIVE_METASTORE_URIS = "metaStoreUris";

    private static final String HBASE_CONFIG = "hbaseConfig";

    public static final String HIVE_PARTITION = "partition";

    private static final String TYPE = "type";

    private static final String HIVE_TYPE = "hiveType";

    private static final String EXTRAL_CONFIG = "extralConfig";

    private static final List<String> MYSQL_NUMBERS = Lists.newArrayList("TINYINT", "SMALLINT", "MEDIUMINT", "INT", "BIGINT", "INT UNSIGNED");

    private static final List<String> CLICKHOUSE_NUMBERS = Lists.newArrayList("UINT8", "UINT16", "UINT32", "UINT64", "INT8", "INT16", "INT32", "INT64");

    private static final List<String> ORACLE_NUMBERS = Lists.newArrayList("INT", "SMALLINT", "NUMBER");

    private static final List<String> SQLSERVER_NUMBERS = Lists.newArrayList("INT", "INTEGER", "SMALLINT", "TINYINT", "BIGINT");

    private static final List<String> POSTGRESQL_NUMBERS = Lists.newArrayList("INT2", "INT4", "INT8", "SMALLINT", "INTEGER", "BIGINT", "SMALLSERIAL", "SERIAL", "BIGSERIAL");

    private static final List<String> DB2_NUMBERS = Lists.newArrayList("SMALLINT", "INTEGER", "BIGINT");

    private static final List<String> GBASE_NUMBERS = Lists.newArrayList("SMALLINT", "TINYINT", "INT", "BIGINT", "FLOAT", "DOUBLE", "DECIMAL", "NUMERIC");

    private static final List<String> DMDB_NUMBERS = Lists.newArrayList("INT", "SMALLINT", "BIGINT", "NUMBER");

    private static final List<String> GREENPLUM_NUMBERS = Lists.newArrayList("SMALLINT", "INTEGER", "BIGINT");

    private static final List<String> KINGBASE_NUMBERS = Lists.newArrayList("BIGINT", "DOUBLE", "FLOAT", "INT4", "INT8", "FLOAT", "FLOAT8", "NUMERIC");

    private static final List<String> INFLUXDB_NUMBERS = Lists.newArrayList("INTEGER");

    private static final Pattern NUMBER_PATTERN = Pattern.compile("NUMBER\\(\\d+\\)");

    private static final Pattern NUMBER_PATTERN2 = Pattern.compile("NUMBER\\((\\d+),([\\d-]+)\\)");

//    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static final String IS_HADOOP_AUTHORIZATION = "hadoop.security.authorization";

    public static final String HADOOP_AUTH_TYPE = "hadoop.security.authentication";

    private static final String KERBEROS_CONFIG = "kerberosConfig";


    /**
     * ssl 认证文件路径
     */
    private static final String KEY_PATH = "keyPath";

    /**
     * ssl 认证文件路径
     */
    private static final String SSL_LOCAL_DIR = "sslLocalDir";

    /**
     * 数据预览：字段名list的key
     */
    private static final String COLUMN_LIST = "columnList";
    /**
     * 数据预览：数据list的key
     */
    private static final String DATA_LIST = "dataList";
    /**
     * 数据预览：数据总条数的key
     */
    private static final String TOTAL = "total";
    /**
     * 数据预览：字段名和数据组合成map对象的list的key
     */
    private static final String DATA_LIST_MAP = "dataListMap";

    /**
     * 底层是postgresql的类型
     */
    private static final Set<Integer> CREATE_TABLE_TO_PG_TABLE = Sets.newHashSet(DataSourceType.LIBRA.getVal(), DataSourceType.GREENPLUM6.getVal(),
            DataSourceType.PostgreSQL.getVal());
    private static final List ORIGIN_TABLE_ALLOW_TYPES = new ArrayList();

    /**
     * 一键生成目标表，不同数据源表名处理正则，像"a"."b" 、 `a`.`b` 、 'a'.'b' 、 [a].[b]
     */
    private static final String TABLE_FORMAT_REGEX = "`|'|\"|\\[|\\]";

    /**
     * 关系型数据库建表模版
     */
    private static final String RDB_CREATE_TABLE_SQL_TEMPLATE = "create table %s ( %s );";

    private static final String SEMICOLON = ";";

    public static final String BASE_URL = "http://nginx.dsg.com:8000";

    public static final String SECRET_KEY = "X4nH8mK9pL2qR5vT7wY3zC1bN4mJ6kL9pQ2sV4xZ7";

    /**
     * 支持一键建表的数据源类型
     */
    private static final Set<DataSourceType> SUPPORT_CREATE_TABLE_DATASOURCES = Sets.newHashSet(DataSourceType.HIVE, DataSourceType.SparkThrift2_1,
            DataSourceType.LIBRA, DataSourceType.PostgreSQL, DataSourceType.HIVE1X, DataSourceType.HIVE3X, DataSourceType.IMPALA,
            DataSourceType.TiDB, DataSourceType.Oracle, DataSourceType.GREENPLUM6, DataSourceType.MySQL, DataSourceType.MySQL8, DataSourceType.MariaDB, DataSourceType.ADB_FOR_PG,
            DataSourceType.SQLServer, DataSourceType.SQLSERVER_2017_LATER, DataSourceType.Clickhouse, DataSourceType.IMPALA, DataSourceType.INCEPTOR, DataSourceType.HIVE3X_MRS,
            DataSourceType.GP_FOR_PG,DataSourceType.GaussDB_FOR_MySQL,DataSourceType.DORIS, DataSourceType.Sybase_jTDS, DataSourceType.Sybase_jConnect);

    /**
     * 支持一键建表的数据源类型名称
     */
    private static final String SUPPORT_CREATE_TABLE_DATASOURCES_NAMES = SUPPORT_CREATE_TABLE_DATASOURCES.stream().map(DataSourceType::getName).collect(Collectors.joining("、"));

    private static final int CORE_POOL_SIZE = 8;
    private static final int MAX_POOL_SIZE = 8;
    private static final int QUEUE_CAPACITY = 16;
    private static final int KEEP_ALIVE = 60;

    private static final Map<String, DatasourceDTO> datasourceDTOMap = new HashMap<>();

    private static ThreadPoolExecutor executor = new ThreadPoolExecutor(CORE_POOL_SIZE, MAX_POOL_SIZE,
            KEEP_ALIVE, TimeUnit.SECONDS, new LinkedBlockingQueue<>(QUEUE_CAPACITY),
            new RdosThreadFactory("kafka-consumer"), new ThreadPoolExecutor.DiscardOldestPolicy());

    private static final String HTTP_POST_OBTAIN_DATASOURCE_INFO = "http://gateway.dsg.com:8000/api/database/datasource/info/getDatasourceInfo/%s";

    /**
     * 设置插件包路径
     *
     * @param dataSourcePluginPath 插件包路径
     */
    public static void initDataSourcePluginPath(String dataSourcePluginPath) {
        if (StringUtils.isNotEmpty(dataSourcePluginPath)) {
            ClientCache.setUserDir(dataSourcePluginPath);
        }
    }

    /**
     * 获取 连接
     * 注意：拿到连接过后需要释放连接！！！
     *
     * @param datasourceDTO 数据源工具参数
     * @return 连接对象
     */
    public static Connection getCon(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        ISourceDTO iSourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(), datasourceDTO.getDataTypeCode(), datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        IClient iClient = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        return iClient.getCon(iSourceDTO);
    }

    /**
     * 执行sql
     *
     * @param id               数据源id
     * @param sql              需要执行的SQL
     * @param isQueryStatement 是否是查询语句(true 查询语句/false 非查询语句)
     */
    public static ExecSQLResult execSQL(@NotNull String id, @NotNull String sql, Boolean isQueryStatement) throws SQLException {
        return execSQLFinal(id, sql, isQueryStatement, null, null);
    }

    /**
     * 执行sql
     * @param id 数据源id
     * @param sql 需要执行的SQL
     * @param isQueryStatement 是否是查询语句(true 查询语句/false 非查询语句)
     * @param tenantCode 租户code
     */
    public static ExecSQLResult execSQL(@NotNull String id, @NotNull String sql, Boolean isQueryStatement, @NotNull String tenantCode) throws SQLException {
        return execSQLFinal(id, sql, isQueryStatement, tenantCode, null);
    }

    public static ExecSQLResult execSQL(@NotNull String id, @NotNull String sql, Boolean isQueryStatement, String tenantCode, DatasourceDTO datasourceDTO) throws SQLException {
        return execSQLFinal(id, sql, isQueryStatement, tenantCode, datasourceDTO);
    }

    /**
     * 执行sql http
     * @param id 数据源id
     * @param sql 需要执行的SQL
     * @param isQueryStatement 是否是查询语句(true 查询语句/false 非查询语句)
     * @param tenantCode 租户code
     * @return 执行结果
     */
    public static Result<ExecSQLResult> execSQLByHTTP(@NotNull String id, @NotNull String sql, Boolean isQueryStatement, String tenantCode) {
        // 创建 OkHttpClient 实例
        OkHttpClient client = new OkHttpClient();
        // 将请求体转换为字符串形式用于签名
        Map<String, String> params = new HashMap<>();
        // 生成签名
        String signature = SignatureUtils.generateSignature(params, SECRET_KEY);
        // 设置请求头
        String url = BASE_URL + "/api/database/datasource/info/execSQL/" + id;
        DatasourceExecSQLDTO execSql = new DatasourceExecSQLDTO();
        execSql.setIsQueryStatement(isQueryStatement);
        execSql.setSql(sql);
        execSql.setTenantCode(tenantCode);
        // 创建请求体
        RequestBody body = RequestBody.create(JSON.toJSONString(execSql), MediaType.parse("application/json"));
        // 创建请求
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("Content-Type", "application/json")
                .addHeader("X-Signature", signature)
                .build();
        // 执行请求
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            if (response.body() == null) {
                throw new RuntimeException("response body is null");
            }
            return JSON.parseObject(response.body().string(), new TypeReference<Result<ExecSQLResult>>(){}.getType());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private static ExecSQLResult execSQLFinal(String id, String sql, Boolean isQueryStatement, String tenantCode, DatasourceDTO datasourceDTO ) throws SQLException {
        ExecSQLResult result = new ExecSQLResult();
        if (datasourceDTO == null) {
            datasourceDTO = getDatasourceDTO(id, tenantCode);
        }

        // 如果是 ODPS(MaxCompute) 类型，则使用 OdpsClient 执行查询
        if (DataSourceTypeEnum.MAXCOMPUTE.getVal().equals(datasourceDTO.getDataTypeCode())) {
            try {
                ISourceDTO iSourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(), datasourceDTO.getDataTypeCode(), datasourceDTO.getKerberosConfig(), Maps.newHashMap());
                IClient iClient = ClientCache.getClient(datasourceDTO.getDataTypeCode());
                SqlQueryDTO queryDTO = SqlQueryDTO.builder()
                    .sql(sql)
                    .build();
                if (isQueryStatement != null && isQueryStatement) {
                    // 查询语句
                    List<Map<String, Object>> queryResult = iClient.executeQuery(iSourceDTO, queryDTO);
                    List<JSONObject> resultList = new ArrayList<>();
                    for (Map<String, Object> row : queryResult) {
                        resultList.add(new JSONObject(row));
                    }
                    result.setResultSet(resultList);
                    result.setIsSuccess(true);
                } else if (isQueryStatement != null && !isQueryStatement) {
                    // 更新语句
                    boolean success = iClient.executeSqlWithoutResultSet(iSourceDTO, queryDTO);
                    result.setIsSuccess(success);
                    result.setAffectedRows(success ? 1 : 0); // ODPS client doesn't return affected rows directly
                } else {
                    // 未知类型语句
                    boolean success = iClient.executeSqlWithoutResultSet(iSourceDTO, queryDTO);
                    result.setIsSuccess(success);
                }
            } catch (Exception e) {
                throw new SQLException("ODPS execute SQL error: " + e.getMessage(), e);
            }
            return result;
        }

        try (Connection connection = getCon(datasourceDTO)) {
            if (isQueryStatement != null && isQueryStatement) {
                try (Statement statement = connection.createStatement()) {
                    ResultSet resultSet = statement.executeQuery(sql);
                    // 用于存储结果的 List
                    List<JSONObject> resultList = new ArrayList<>();
                    // 获取元数据
                    ResultSetMetaData metaData = resultSet.getMetaData();
                    int columnCount = metaData.getColumnCount();  // 获取列的数量
                    // 遍历结果集
                    while (resultSet.next()) {
                        // 创建 JSON 对象来存储一行数据
                        JSONObject row = new JSONObject();
                        // 遍历每一列
                        for (int i = 1; i <= columnCount; i++) {
                            String columnName = metaData.getColumnLabel(i);  // 获取列名
                            Object columnValue = resultSet.getObject(i);     // 获取列值
                            // 处理 oracle.sql.TIMESTAMP
                            if (Objects.nonNull(columnValue) && columnValue.getClass().getName().contains("oracle.sql.TIMESTAMP")) {
                                columnValue = columnValue.toString();
                            }
                            row.put(columnName, columnValue);                // 将数据放入 JSON 中
                        }
                        // 将这一行数据添加到结果列表中
                        resultList.add(row);
                    }
                    result.setResultSet(resultList);
                    result.setIsSuccess(true);
                    resultSet.close();
                }
            } else if (isQueryStatement != null && !isQueryStatement) {
                try (Statement statement = connection.createStatement()) {
                    int affectedRows = statement.executeUpdate(sql);
                    result.setAffectedRows(affectedRows);
                    result.setIsSuccess(true);
                }
            } else {
                try (Statement statement = connection.createStatement()) {
                    boolean flag = statement.execute(sql);
                    result.setIsSuccess(flag);
                }
            }
        }
        return result;
    }

    /**
     * 获取数据源信息
     * @param id 数据源id
     * @return DatasourceDTO
     */
    private static DatasourceDTO getDatasourceDTO(String id, String tenantCode) {
        DatasourceDTO result = null;
        // HTTP 请求
        String url = String.format(HTTP_POST_OBTAIN_DATASOURCE_INFO, id);
        if (StringUtils.isNotBlank(tenantCode)) {
            url = url + "?tenantCode=" + tenantCode;
        }
        RestTemplate restTemplate = new RestTemplate();
        HttpEntity<String> request = new HttpEntity<>(null, null);
        String resultJSON = restTemplate.postForObject(url,request,String.class);
        if (StringUtils.isNotEmpty(resultJSON)) {
            JSONObject data = JSONObject.parseObject(resultJSON).getJSONObject("data");
            if (null == data) {
                throw new DatasourceDefException(CAN_NOT_FIND_DATA_SOURCE);
            }
            result = JSONObject.parseObject(data.toJSONString(), DatasourceDTO.class);
        }
        return result;
    }

    /**
     * 获取流式结果集
     * 注意：拿到连接过后需要释放各个资源！！！
     *
     * @param datasourceDTO 数据源工具参数
     * @return 流式结果集
     */
    public static ConnectorResultEntity getResultSetStream(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        ISourceDTO iSourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(), datasourceDTO.getDataTypeCode(), datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder().sql(datasourceDTO.getSql()).build();
        IClient iClient = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        return iClient.getResultSetStream(iSourceDTO, sqlQueryDTO, datasourceDTO.isEnableStream(), datasourceDTO.isAutoCommit());
    }

    /**
     * 获取驱动名
     *
     * @param datasourceDTO 数据源工具参数
     * @return driverClassName 驱动名
     */
    public static String getDriverClassName(@NotNull DatasourceDTO datasourceDTO) {
        String driverClassName;
        if (datasourceDTO.getDataTypeCode() == null) {
            driverClassName = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion()).getDriverClassName();
        } else if (DataSourceTypeEnum.SQLServer.getVal().equals(datasourceDTO.getDataTypeCode())
                || DataSourceTypeEnum.SQLSERVER_2017_LATER.getVal().equals(datasourceDTO.getDataTypeCode())) {
            //SQLServer还要根据url的写法来获取不同的驱动名
            JSONObject dataJsonMap = datasourceDTO.getDataJsonMap();
            String jdbcUrl = dataJsonMap.getString("jdbcUrl");
            if (StringUtils.isEmpty(jdbcUrl)) {
                //如果没传jdbcUrl，默认给"com.microsoft.sqlserver.jdbc.SQLServerDriver"
                driverClassName = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
            } else {
                if (StringUtils.startsWithIgnoreCase(jdbcUrl.trim(), "jdbc:sqlserver")) {
                    driverClassName = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
                } else {
                    driverClassName = "net.sourceforge.jtds.jdbc.Driver";
                }
            }
        } else {
            //直接根据数据源类型编码获取驱动名
            driverClassName = DataSourceTypeEnum.valOf(datasourceDTO.getDataTypeCode()).getDriverClassName();
        }
        return driverClassName;
    }

    /**
     * 关闭数据库资源信息
     */
    public static void closeDBResources(ResultSet rs, Statement stmt, Connection conn) {
        DBUtil.closeDBResources(rs, stmt, conn);
    }

    /**
     * 数据源连通性测试
     *
     * @param datasourceDTO     数据源工具参数
     * @param confMap           kerberos参数
     * @param localKerberosPath kerberos路径
     * @return 连接成功true / 连接失败false
     */
    public static Boolean checkConnectionWithConf(@NotNull DatasourceDTO datasourceDTO,
                                                  Map<String, Object> confMap, String localKerberosPath) {
        JSONObject dataJsonMap = datasourceDTO.getDataJsonMap();
        String jdbcUrl = dataJsonMap.getString("jdbcUrl");
//        //jdbc系列需要先验证ip、端口是否能ping通
//        if (StringUtils.isNotEmpty(jdbcUrl)) {
//            if (!NetUtils.telnet(JdbcUrlUtils.getIp(jdbcUrl), JdbcUrlUtils.getPort(jdbcUrl))) {
//                throw new RdosDefineException("IP地址或端口号输入错误");
//            }
//        }

        //jdbc系列需要先验证ip、端口是否能ping通
        if (StringUtils.isNotEmpty(jdbcUrl)) {
            //如果jdbcUrl中 含有ip 或者 port字符串 则直接抛错
            if(jdbcUrl.contains("127.0.0.1")){
                throw new RdosDefineException("IP地址或端口号输入错误");
            }
            //oracle 做一下处理
            if (DataSourceTypeEnum.Oracle.getDataType().equalsIgnoreCase(datasourceDTO.getDataType()) ||
                    DataSourceTypeEnum.Doris_JDBC.getDataType().equalsIgnoreCase(datasourceDTO.getDataType()) ||
                    DataSourceTypeEnum.MySQL.getDataType().equalsIgnoreCase(datasourceDTO.getDataType())) {
                //获取ip
                List<String> hosts = JdbcUrlUtils.getHosts(jdbcUrl);
                //获取ports
                List<String> ports = JdbcUrlUtils.getPorts(jdbcUrl);
                if(hosts == null || ports == null){
                    throw new RdosDefineException("IP地址或端口号输入错误");
                }
                //如果hosts的数量为1，则直接调用NetUtils.telnet 大于一 则循环调用
                if (hosts.size() == 1) {
                    if (!NetUtils.telnet(hosts.get(0), Integer.parseInt(ports.get(0)))) {
                        throw new RdosDefineException("IP地址或端口号输入错误");
                    }
                } else {
                    for (int h = 0; h < hosts.size(); h++) {
                        if(NetUtils.telnet(hosts.get(h), Integer.parseInt(ports.get(h)))){
                            //有一个连通则结束循环
                           break;
                        }
                    }
                }
            }else {
                if (!NetUtils.telnet(JdbcUrlUtils.getIp(jdbcUrl), JdbcUrlUtils.getPort(jdbcUrl))) {
                    throw new RdosDefineException("IP地址或端口号输入错误");
                }
            }

        }

        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());

        // 替换相对绝对路径
        Map<String, Object> tempConfMap = null;
        if (MapUtils.isNotEmpty(confMap)) {
            tempConfMap = Maps.newHashMap(confMap);
            if (StringUtils.isNotEmpty(localKerberosPath)) {
                IKerberos kerberos = ClientCache.getKerberos(typeEnum.getVal());
                kerberos.prepareKerberosForConnect(tempConfMap, localKerberosPath);
            }
        }
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        // 测试连通性
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), tempConfMap, Maps.newHashMap());
        return ClientCache.getClient(typeEnum.getVal()).testCon(sourceDTO);
    }

    /**
     * 获取所有schema
     *
     * @param datasourceDTO 数据源工具参数
     * @return 所有schema
     */
    public static List<String> getAllSchemas(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        JSONObject dataJson = datasourceDTO.getDataJsonMap();
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(dataJson, datasourceDTO.getDataTypeCode(), datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        IClient client = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        return client.getAllDatabases(sourceDTO, SqlQueryDTO.builder().schema(datasourceDTO.getSchema()).build());
    }

    /**
     * 获取当前使用的数据库
     *
     * @param datasourceDTO 数据源工具参数
     * @return 当前使用的数据库
     */
    public static String getCurrentDatabase(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(), datasourceDTO.getDataTypeCode(), datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        if (sourceDTO instanceof RdbmsSourceDTO) {
            IClient client = ClientCache.getClient(datasourceDTO.getDataTypeCode());
            return client.getCurrentDatabase(sourceDTO);
        }
        return null;
    }

    /**
     * 获取当前使用的模式，如kingbase数据库下还有schema模式
     *
     * @param datasourceDTO 数据源工具参数
     * @return 当前使用的数据库模式
     */
    public static String getCurrentSchema(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(), datasourceDTO.getDataTypeCode(), datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        if (sourceDTO instanceof RdbmsSourceDTO) {
            IClient client = ClientCache.getClient(datasourceDTO.getDataTypeCode());
            return client.getCurrentSchema(sourceDTO);
        }
        return null;
    }

    /**
     * 获取表列表
     *
     * @param datasourceDTO 数据源工具参数
     * @return 表/视图名称 列表
     */
    public static List<String> tableList(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        List<String> tables;
        JSONObject dataJsonMap = datasourceDTO.getDataJsonMap();
        IClient client = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(dataJsonMap, datasourceDTO.getDataTypeCode(),
                datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder()
                .schema(datasourceDTO.getSchema())
                .tableNamePattern(datasourceDTO.getTableName())
                .view(datasourceDTO.isView())
                .limit(5000).build();
        //如果是hive类型的数据源  过滤脏数据表 和 临时表
        tables = client.getTableList(sourceDTO, sqlQueryDTO);
        return tables;
    }

    /**
     * 返回所有的表和视图名称
     *
     * @param datasourceDTO 数据源工具参数
     * @return 表/视图名称 列表（包含区分视图和表的字段）
     */
    public static List<TableViewDTO> getTableAndViewList(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        List<TableViewDTO> tables;
        JSONObject dataJsonMap = datasourceDTO.getDataJsonMap();
        IClient client = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(dataJsonMap, datasourceDTO.getDataTypeCode(),
                datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder()
                .schema(datasourceDTO.getSchema())
                .tableNamePattern(datasourceDTO.getTableName())
                .view(datasourceDTO.isView())
                .build();
        tables = client.getTableAndViewList(sourceDTO, sqlQueryDTO);
        return tables;
    }

    /**
     * 返回所有的表和视图名称
     *
     * @param datasourceDTO 数据源工具参数
     * @return 表/视图名称 列表（包含区分视图和表的字段）
     */
    public static List<TableViewDTO> getTableListByEs(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        List<TableViewDTO> tables = new ArrayList<>();
        JSONObject dataJsonMap = datasourceDTO.getDataJsonMap();
        IClient client = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(dataJsonMap, datasourceDTO.getDataTypeCode(),
                datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder()
                .schema(datasourceDTO.getSchema())
                .tableNamePattern(datasourceDTO.getTableName())
                .view(datasourceDTO.isView())
                .build();
        //获取所有索引
        List allDatabases = client.getAllDatabases(sourceDTO, sqlQueryDTO);
        //获取索引所有类型
        for (Object allDatabase : allDatabases) {
            SqlQueryDTO sqlQueryDTO1 = SqlQueryDTO.builder()
                    .schema(String.valueOf(allDatabase))
                    .build();
            IClient client1 = ClientCache.getClient(datasourceDTO.getDataTypeCode());
            List tableList = client1.getTableList(sourceDTO, sqlQueryDTO1);
            if (tableList.isEmpty()) {
                TableViewDTO tableViewDTO = new TableViewDTO();
                //表名 以type-index 拼接
                tableViewDTO.setName(String.valueOf(allDatabase));
                tableViewDTO.setType("table");
                tables.add(tableViewDTO);
                continue;
            }
            for (Object o : tableList) {
                TableViewDTO tableViewDTO = new TableViewDTO();
                //表名 以type<index> 拼接
                tableViewDTO.setName(String.valueOf(allDatabase) + "<" + String.valueOf(o) + ">");
                tableViewDTO.setType("table");
                tables.add(tableViewDTO);
            }
        }
        return tables;
    }

    /**
     * 返回所有的索引
     *
     * @param datasourceDTO 数据源工具参数
     * @return 表/视图名称 列表（包含区分视图和表的字段）
     */
    public static List<String> getAllIndexByEs(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        JSONObject dataJsonMap = datasourceDTO.getDataJsonMap();
        IClient client = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(dataJsonMap, datasourceDTO.getDataTypeCode(),
                datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder()
                .schema(datasourceDTO.getSchema())
                .tableNamePattern(datasourceDTO.getTableName())
                .view(datasourceDTO.isView())
                .build();
        //获取所有索引
        List allDatabases = client.getAllDatabases(sourceDTO, sqlQueryDTO);

        return allDatabases;
    }

    /**
     * 返回所有的索引表的字段
     *
     * @param datasourceDTO 数据源工具参数
     * @return 表/视图名称 列表（包含区分视图和表的字段）
     */
    public static List<JSONObject> getAllIndexColumnByEs(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        JSONObject dataJsonMap = datasourceDTO.getDataJsonMap();
        IClient client = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(dataJsonMap, datasourceDTO.getDataTypeCode(),
                datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder()
                .schema(datasourceDTO.getSchema())
                .tableName(datasourceDTO.getTableName())
                .view(datasourceDTO.isView())
                .build();
        //获取所有索引z
        List<ColumnMetaDTO> columnMetaData = client.getColumnMetaData(sourceDTO, sqlQueryDTO);
        //字段排序
        List<JSONObject> list = new ArrayList<>();
        int columnSort = 1;
        if (CollectionUtils.isNotEmpty(columnMetaData)) {
            for (ColumnMetaDTO columnMetaDTO : columnMetaData) {
                JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(columnMetaDTO));
                jsonObject.put("columnSort", columnSort++);
                list.add(jsonObject);
            }
        }
        return list;
    }


    /**
     * 获取表占用存储
     *
     * @param datasourceDTO 数据源工具参数
     * @return 占用存储，单位 B
     */
    public static Long getTableSize(@NotNull DatasourceDTO datasourceDTO) {
        Integer dataTypeCode = datasourceDTO.getDataTypeCode();
        //FIXME impala暂时没有获取表占用存储
        if (DataSourceType.IMPALA.getVal().equals(dataTypeCode)) {
            log.warn("[{}]没有表占用存储，返回0", DataSourceTypeEnum.valOf(dataTypeCode).getDataType());
            return 0L;
        }
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        ITable tableClient = ClientCache.getTable(datasourceDTO.getDataTypeCode());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(), datasourceDTO.getDataTypeCode(), datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        if (DataSourceType.Oracle.getVal().equals(datasourceDTO.getDataTypeCode())) {
            datasourceDTO.setDbName(null);
        }
        return tableClient.getTableSize(sourceDTO, datasourceDTO.getSchema(), datasourceDTO.getTableName());
    }

    /**
     * 获取字符集
     *
     * @param datasourceDTO 数据源工具参数
     * @return 字符集
     */
    public static String getCharacterSet(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        Integer dataTypeCode = datasourceDTO.getDataTypeCode();
        //FIXME clickhouse和impala  hive 没有字符集的概念
        if (DataSourceTypeEnum.ClickHouse.getVal().equals(dataTypeCode)
                || DataSourceType.IMPALA.getVal().equals(dataTypeCode)
                || DataSourceType.HIVE1X.getVal().equals(dataTypeCode)
                || DataSourceType.HIVE.getVal().equals(dataTypeCode)
                || DataSourceType.HIVE3X.getVal().equals(dataTypeCode)
                || DataSourceType.INCEPTOR.getVal().equals(dataTypeCode)
        ) {
            log.warn("[{}]没有字符集，返回空字符串", DataSourceTypeEnum.valOf(dataTypeCode).getDataType());
            return "";
        }
        IClient client = ClientCache.getClient(dataTypeCode);
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(), dataTypeCode,
                datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder().tableName(datasourceDTO.getTableName()).build();
        return client.getCharacterSet(sourceDTO, sqlQueryDTO);
    }

    /**
     * 获取排序规则
     *
     * @param datasourceDTO 数据源工具参数
     * @return 排序规则
     */
    public static String getCharacterCollation(@NotNull DatasourceDTO datasourceDTO) {
        Integer dataTypeCode = datasourceDTO.getDataTypeCode();
        //FIXME impala hive没有排序规则概念
        if (DataSourceType.IMPALA.getVal().equals(dataTypeCode)
                || DataSourceType.HIVE1X.getVal().equals(dataTypeCode)
                || DataSourceType.HIVE.getVal().equals(dataTypeCode)
                || DataSourceType.HIVE3X.getVal().equals(dataTypeCode)
                || DataSourceType.INCEPTOR.getVal().equals(dataTypeCode)
        ) {
            log.warn("[{}]没有排序规则，返回空字符串", DataSourceTypeEnum.valOf(dataTypeCode).getDataType());
            return "";
        }
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        IClient client = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(), datasourceDTO.getDataTypeCode(),
                datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder().tableName(datasourceDTO.getTableName()).build();
        return client.getCharacterCollation(sourceDTO, sqlQueryDTO);
    }

    /**
     * 获取表行数
     *
     * @param datasourceDTO 数据源工具参数
     * @return 表行数
     */
    public static Long getTableRows(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        IClient client = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(), datasourceDTO.getDataTypeCode(),
                datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder().tableName(datasourceDTO.getTableName()).schema(datasourceDTO.getSchema()).build();
        return client.getTableRows(sourceDTO, sqlQueryDTO);
    }

    /**
     * 获取表字段信息
     *
     * @param datasourceDTO 数据源工具参数
     * @param isIncludePart 是否需要分区字段（默认为false）
     * @return 结果
     */
    public static List<JSONObject> tableColumn(@NotNull DatasourceDTO datasourceDTO, Boolean isIncludePart) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        //获取类型
        String dataType = datasourceDTO.getDataType();
        //如果是es 调用getAllIndexColumnByEs 这个方法获取字段
        if (DataSourceTypeEnum.ES.getDataType().equalsIgnoreCase(dataType)) {
            List<JSONObject> allIndexColumnByEs = getAllIndexColumnByEs(datasourceDTO);
            return allIndexColumnByEs;

        }
        String tableName = datasourceDTO.getTableName();
        return getTableColumnIncludePart(datasourceDTO, tableName, isIncludePart, datasourceDTO.getSchema());
    }

    /**
     * 查询表所属字段 可以选择是否需要分区字段
     *
     * @param datasourceDTO 数据源工具参数
     * @param tableName     表名
     * @param part          是否需要分区字段
     * @return 结果
     */
    private static List<JSONObject> getTableColumnIncludePart(DatasourceDTO datasourceDTO, String tableName, Boolean part, String schema) {
        try {
            if (datasourceDTO == null) {
                throw new RdosDefineException(CAN_NOT_FIND_DATA_SOURCE);
            }
            if (part == null) {
                part = false;
            }
            JSONObject dataJson = datasourceDTO.getDataJsonMap();
            IClient iClient = ClientCache.getClient(datasourceDTO.getDataTypeCode());
            boolean isCapitalization = false;
            Map<String, Object> extraParam = datasourceDTO.getExtraParam();
            if (extraParam != null) {
                isCapitalization = Boolean.parseBoolean(extraParam.get("isCapitalization") == null ? "false" : extraParam.get("isCapitalization").toString());
            }
            SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder()
                    .tableName(tableName)
                    .schema(schema)
                    .dbName(datasourceDTO.getDbName())
                    .tableTypes(datasourceDTO.isCurrentView() ? new String[]{"VIEW"} : new String[]{})
                    .filterPartitionColumns(part)
                    .isCapitalization(isCapitalization)
                    .build();
            //填充kerberos认证
            Map<String, Object> kerberosConfig = datasourceDTO.getKerberosConfig();
            ISourceDTO iSourceDTO = SourceDTOType.getSourceDTO(dataJson, datasourceDTO.getDataTypeCode(), kerberosConfig, Maps.newHashMap());
            List<ColumnMetaDTO> columnMetaData = iClient.getColumnMetaData(iSourceDTO, sqlQueryDTO);
            List<JSONObject> list = new ArrayList<>();
            //字段排序
            int columnSort = 1;
            if (CollectionUtils.isNotEmpty(columnMetaData)) {
                for (ColumnMetaDTO columnMetaDTO : columnMetaData) {
                    JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(columnMetaDTO));
                    jsonObject.put("isPart", columnMetaDTO.getPart());
                    jsonObject.put("columnSort", columnSort++);
                    list.add(jsonObject);
                }
            }
            return list;
        } catch (Exception e) {
            throw new RdosDefineException(ErrorCode.GET_COLUMN_ERROR+"异常为："+e.getMessage(), e);
        }
    }

    public static void previewData2Map(@NotNull JSONObject previewData) {
        List<Map<String, Object>> dataListMap = new ArrayList<>();
        List<Object> dataList = (List<Object>) previewData.get("dataList");
        List<String> columnList = (List<String>) previewData.get("columnList");
        if (Objects.isNull(dataList) || CollectionUtils.isEmpty(columnList)) {
            previewData.put(DATA_LIST_MAP, dataListMap);
            log.error("数据传入对象为空，转换终止，返回原始数据");
            return;
        }

        for (Object o : dataList) {
            if (o instanceof List) {
                List<String> list = (List<String>) o;
                if (CollectionUtils.isNotEmpty(list)) {
                    Map<String, Object> map = new HashMap<>();
                    for (int i = 0; i < list.size(); i++) {
                        map.put(columnList.get(i), list.get(i));
                    }
                    dataListMap.add(map);
                }
            }
        }
        previewData.put(DATA_LIST_MAP, dataListMap);
    }

    /**
     * 获取预览数据SQL
     *
     * @param datasourceDTO 数据源工具参数
     * @return 预览数据SQL
     */
    public static String previewSql(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }

        JSONObject dataJson = datasourceDTO.getDataJsonMap();
        String tableName = datasourceDTO.getTableName();
        //kerberos 直接从传入的数据源DTO中拿kerberos配置
        Map<String, Object> kerberosConfig = datasourceDTO.getKerberosConfig();
        IClient iClient = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        ISourceDTO iSourceDTO = SourceDTOType.getSourceDTO(dataJson, datasourceDTO.getDataTypeCode(), kerberosConfig, Maps.newHashMap());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder()
                .schema(datasourceDTO.getSchema())
                .tableName(tableName)
                .whereRelation(datasourceDTO.getWhereRelation())
                .whereConditions(datasourceDTO.getWhereConditions())
                .impalaOrderByColumn(datasourceDTO.getImpalaOrderByColumn())
                .build();
        if (datasourceDTO.getPreviewNum() == null) {
            sqlQueryDTO.setPreviewNum(1000);
        } else if (datasourceDTO.getPreviewNum() == -1) {
            sqlQueryDTO.setPreviewNum(null);
        } else {
            sqlQueryDTO.setPreviewNum(datasourceDTO.getPreviewNum());
        }

        //预览数据SQL
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSourceDTO;
        String previewSql = iClient.dealSql(rdbmsSourceDTO, sqlQueryDTO);
        previewSql = previewSql + iClient.dealWhereSql(rdbmsSourceDTO, previewSql, sqlQueryDTO);

        if (StringUtils.isNotBlank(previewSql)) {
            List<String> selectColumns = datasourceDTO.getSelectColumns();
            if (CollectionUtils.isNotEmpty(selectColumns)) {
                List<String> collect = selectColumns.stream().map(e -> {
                    DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.valOf(datasourceDTO.getDataTypeCode());
                    String columnPrefix = dataSourceTypeEnum.getColumnPrefix();
                    String columnSuffix = dataSourceTypeEnum.getColumnSuffix();
                    return columnPrefix + e + columnSuffix;
                }).collect(Collectors.toList());
                String join = String.join(",", collect);
                previewSql = previewSql.replace("*", join);
            }
        }

        return previewSql;
    }

    /**
     * 数据同步-获得预览数据
     *
     * @param datasourceDTO 数据源工具参数
     * @return 结果
     */
    public static JSONObject preview(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        String tableName = datasourceDTO.getTableName();
        /*StringBuffer newTableName = new StringBuffer();
        if (DataSourceType.SQLServer.getVal().equals(datasourceDTO.getDataTypeCode()) && StringUtils.isNotBlank(tableName)) {
            if (!tableName.contains("[")) {
                final String[] tableNames = tableName.split("\\.");
                if (tableNames.length > 1) {
                    for (int i = 0; i < tableNames.length - 1; i++) {
                        newTableName.append("[").append(tableNames[i]).append("]").append(".");
                    }
                    newTableName.append(tableNames[tableNames.length - 1]).append(".");
                    tableName = newTableName.substring(0, newTableName.length() - 1);
                }
            }
        }*/
        JSONObject dataJson = datasourceDTO.getDataJsonMap();
        //获取字段信息
        List<String> columnList = new ArrayList<>();
        //获取数据
        List<List<String>> dataList;
        int total = 0;
        try {
            List<JSONObject> columnJson = getTableColumn(datasourceDTO, tableName, datasourceDTO.getSchema());
            if (CollectionUtils.isNotEmpty(columnJson)) {
                for (JSONObject columnMetaDTO : columnJson) {
                    columnList.add(columnMetaDTO.getString("key"));
                }
            }
            //kerberos 直接从传入的数据源DTO中拿kerberos配置
            Map<String, Object> kerberosConfig = datasourceDTO.getKerberosConfig();
            IClient iClient = ClientCache.getClient(datasourceDTO.getDataTypeCode());
            ISourceDTO iSourceDTO = SourceDTOType.getSourceDTO(dataJson, datasourceDTO.getDataTypeCode(), kerberosConfig, Maps.newHashMap());
            SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder()
                    .schema(datasourceDTO.getSchema())
                    .tableName(tableName)
                    .whereRelation(datasourceDTO.getWhereRelation())
                    .whereConditions(datasourceDTO.getWhereConditions())
                    .impalaOrderByColumn(datasourceDTO.getImpalaOrderByColumn())
                    .build();
            if (datasourceDTO.getPreviewNum() == null) {
                sqlQueryDTO.setPreviewNum(1000);
            } else if (datasourceDTO.getPreviewNum() == -1) {
                sqlQueryDTO.setPreviewNum(null);
            } else {
                sqlQueryDTO.setPreviewNum(datasourceDTO.getPreviewNum());
            }

            if (datasourceDTO.getPageNum() != null && datasourceDTO.getPageNum() > 0) {
                sqlQueryDTO.setPageNum(datasourceDTO.getPageNum());
                total = iClient.getPreviewRows(iSourceDTO, sqlQueryDTO);
            }
            dataList = iClient.getPreview(iSourceDTO, sqlQueryDTO);
            // 暂未发现有 dataList 第一行返回是字段名的情况，暂时去除
            if (DataSourceType.getRDBMS().contains(datasourceDTO.getDataTypeCode()) ) {
                //因为会把字段名也会返回 所以要去除第一行,但是doris  doris1 且doris2排除外
                if (!dataList.isEmpty() && !(datasourceDTO.getDataTypeCode().equals(DataSourceType.DORIS.getVal()) ||
                        datasourceDTO.getDataTypeCode().equals(DataSourceType.DORIS1.getVal()) ||
                        datasourceDTO.getDataTypeCode().equals(DataSourceType.DORIS2.getVal()))) {
                    boolean flag = false;
                    for (String data : dataList.get(0)) {
                        if (flag) {
                            break;
                        }
                        for (String columnName : columnList) {
                            if (data != null && data.equals(columnName)) {
                                flag = true;
                                break;
                            }
                        }
                    }
                    if (flag) {
                        dataList = dataList.subList(1, dataList.size());
                    }
                }
            }
        } catch (Exception e) {
            log.error("datasource preview end with error.", e);
            if(e.getMessage().contains("org.apache.hadoop.hbase.TableNotEnabledException")){
                throw new DtLoaderException(String.format("表%s,目前处于禁用状态，因此无法进行任何读写操作", datasourceDTO.getTableName()));
            }else {
                throw new RdosDefineException("连接数据源失败，无法加载数据");
            }
        }

        JSONObject preview = new JSONObject(2);
        if (CollectionUtils.isNotEmpty(datasourceDTO.getSelectColumns())) {
            //组装需要查询的字段
            List<String> selectColumns = datasourceDTO.getSelectColumns();
            List<List<String>> selectDataList = new ArrayList<>();

            int size = selectColumns.size();
            List<Integer> indexList = new ArrayList<>(size);
            List<String> selectColumnList = new ArrayList<>();
            for (int i = 0; i < columnList.size(); i++) {
                String column = columnList.get(i);
                if (selectColumns.contains(column)) {
                    selectColumnList.add(column);
                    indexList.add(i);
                }
            }

            if (CollectionUtils.isNotEmpty(dataList)) {
                for (List<String> data : dataList) {
                    if (CollectionUtils.isNotEmpty(data)) {
                        List<String> selectData = new ArrayList<>();
                        for (int i = 0; i < data.size(); i++) {
                            if (indexList.contains(i)) {
                                selectData.add(data.get(i));
                            }
                        }
                        selectDataList.add(selectData);
                    }
                }
            }

            preview.put(COLUMN_LIST, selectColumnList);
            preview.put(DATA_LIST, selectDataList);
        } else {
            preview.put(COLUMN_LIST, columnList);
            preview.put(DATA_LIST, dataList);
        }
        preview.put(TOTAL, total);

        return preview;
    }

    /**
     * 获取表所属字段 不包括分区字段
     */
    private static List<JSONObject> getTableColumn(DatasourceDTO datasourceDTO, String tableName, String schema) {
        try {
            return getTableColumnIncludePart(datasourceDTO, tableName, false, schema);
        } catch (final Exception e) {
            throw new RdosDefineException("获取表字段异常", e);
        }

    }

    /**
     * ddl建表
     *
     * @param datasourceDTO 数据源工具参数
     * @return 结果
     */
    public static String ddlCreateTable(@NotNull DatasourceDTO datasourceDTO) {
        String sql = datasourceDTO.getSql();
        if (StringUtils.isNotBlank(sql)) {
            sql = sql.trim();
        } else {
            throw new RdosDefineException("Sql不能为空");
        }
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        if (DataSourceType.Oracle.getVal().equals(datasourceDTO.getDataTypeCode())) {
            return dealOracleCreateSql(datasourceDTO, sql);
        }
        onlyNeedOneSql(sql);
        if (!SqlFormatUtil.isCreateSql(sql)) {
            throw new RdosDefineException(ErrorCode.ONLY_EXECUTE_CREATE_TABLE_SQL);
        }
        sql = SqlFormatUtil.init(sql).removeEndChar().getSql();
        String tableName = CreateTableSqlParseUtil.parseTableName(sql);
        executeOnSpecifySourceWithOutResult(datasourceDTO, Lists.newArrayList(sql));
        return tableName;
    }

    /**
     * 处理oracle类型建表sql
     */
    private static String dealOracleCreateSql(DatasourceDTO datasourceDTO, String sql) {
        if (!sql.endsWith(SEMICOLON)) {
            sql = sql + SEMICOLON;
        }
        List<String> sqlList = SqlFormatUtil.splitSqlText(sql);
        if (CollectionUtils.isNotEmpty(sqlList) && !SqlFormatUtil.isCreateSql(sqlList.get(0))) {
            throw new RdosDefineException(ErrorCode.ONLY_EXECUTE_CREATE_TABLE_SQL);
        }
        String tableName = CreateTableSqlParseUtil.parseTableName(sqlList.get(0));
        executeOnSpecifySourceWithOutResult(datasourceDTO, sqlList);
        return tableName.toUpperCase(Locale.ROOT);
    }

    /**
     * @param datasourceDTO 数据源id
     * @param sqlList       拼写sql
     */
    private static void executeOnSpecifySourceWithOutResult(DatasourceDTO datasourceDTO, List<String> sqlList) {

        DataSourceType dataSourceType = DataSourceType.getSourceType(datasourceDTO.getDataTypeCode());
        if (!SUPPORT_CREATE_TABLE_DATASOURCES.contains(dataSourceType)) {
            throw new RdosDefineException(String.format("只支持创建%s数据源表", SUPPORT_CREATE_TABLE_DATASOURCES_NAMES));
        }
        JSONObject json = datasourceDTO.getDataJsonMap();

        try {
            Map<String, Object> expandConfigPrepare = expandConfigPrepare(datasourceDTO);
            ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(json, datasourceDTO.getDataTypeCode(), datasourceDTO.getKerberosConfig(), expandConfigPrepare);
            IClient iClient = ClientCache.getClient(dataSourceType.getVal());
            Connection con = iClient.getCon(sourceDTO);
            for (int i = 0; i < sqlList.size(); i++) {
                if (StringUtils.isNotBlank(sqlList.get(i))) {
                    DBUtil.executeSqlWithoutResultSet(con, sqlList.get(i), false);
                }
            }
        } catch (Exception e) {
            throw new RdosDefineException(String.format("执行sql：%s 异常", StringUtils.join(sqlList, ",")), e);
        }
    }

    /**
     * 拓展配置 ssl 配置信息等
     */
    private static Map<String, Object> expandConfigPrepare(DatasourceDTO datasourceDTO) {
        JSONObject dataJson = datasourceDTO.getDataJsonMap();
        String sftpDir = dataJson.getString(KEY_PATH);
        if (StringUtils.isEmpty(sftpDir)) {
            return Collections.emptyMap();
        }
        Map<String, Object> config = new HashMap<>();
        //TODO
//        Map<String, String> sftpMap = getSftpMap(sourceInfo.getTenantId());
//        DataSourceUtils.downloadFileFromSftp(sftpDir, DataSourceUtils.getLocalSslDir(sourceId), sftpMap, null);
//        config.put(SSL_LOCAL_DIR, DatasourceUtils.getLocalSslDir(sourceId));
        return config;
    }

    private static void onlyNeedOneSql(String sql) {
        int unEmptySqlNum = 0;
        List<String> split = Strings.splitIgnoreQuota(sql, ';');
        for (String s : split) {
            if (StringUtils.isNotEmpty(s.trim())) {
                unEmptySqlNum++;
            }
        }
        if (unEmptySqlNum == 0) {
            throw new RdosDefineException("Sql不能为空");
        } else if (unEmptySqlNum > 1) {
            throw new RdosDefineException("仅支持执行一条sql语句");
        }
    }

    /**
     * 获取表注释
     *
     * @param datasourceDTO 数据源工具参数
     * @return 表注释
     */
    public static String getTableMetaComment(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        JSONObject dataJson = datasourceDTO.getDataJsonMap();
        ISourceDTO iSourceDTO = SourceDTOType.getSourceDTO(dataJson, datasourceDTO.getDataTypeCode(), datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder()
                .tableName(datasourceDTO.getTableName())
                .schema(datasourceDTO.getSchema())
                .build();
        IClient iClient = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        return iClient.getTableMetaComment(iSourceDTO, sqlQueryDTO);
    }

    /**
     * 判断数据库是否存在
     *
     * @param datasourceDTO 数据源工具参数
     * @return true存在/false不存在
     */
    public static Boolean isDatabaseExists(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        ISourceDTO iSourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(), datasourceDTO.getDataTypeCode(), datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        IClient iClient = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        return iClient.isDatabaseExists(iSourceDTO, datasourceDTO.getDbName());
    }

    /**
     * 获取表字段java标准格式
     *
     * @param datasourceDTO 数据源工具参数
     * @return java字段类型
     */
    public static List<String> getColumnClassInfo(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        ISourceDTO iSourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(), datasourceDTO.getDataTypeCode(), datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder()
                .tableName(datasourceDTO.getTableName())
                .build();
        if (CollectionUtils.isNotEmpty(datasourceDTO.getColumns())) {
            sqlQueryDTO.setColumns(datasourceDTO.getColumns());
        }
        IClient iClient = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        return iClient.getColumnClassInfo(iSourceDTO, sqlQueryDTO);
    }

    /**
     * 执行SQL（支持预编译，只需设置preFields预编译字段）
     *
     * @param datasourceDTO 数据源工具参数
     * @return 结果
     */
    public static List<Map<String, Object>> executeQuery(@NotNull DatasourceDTO datasourceDTO) {
        try {
            //设置插件包路径
            if (datasourceDTO.isInitDataSourcePluginPath()) {
                initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
            }
            ISourceDTO iSourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(), datasourceDTO.getDataTypeCode(), datasourceDTO.getKerberosConfig(), Maps.newHashMap());
            SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql(datasourceDTO.getSql()).build();
            if (datasourceDTO.getQueryTimeout() != null && datasourceDTO.getQueryTimeout() > 0) {
                //查询超时时间
                queryDTO.setQueryTimeout(datasourceDTO.getQueryTimeout());
            }
            if (CollectionUtils.isNotEmpty(datasourceDTO.getPreFields())) {
                //设置preFields预编译字段
                queryDTO.setPreFields(datasourceDTO.getPreFields());
            }
            Integer previewNum = datasourceDTO.getPreviewNum();
            if (previewNum != null && previewNum >= 0) {
                queryDTO.setLimit(previewNum);
            }
            IClient iClient = ClientCache.getClient(datasourceDTO.getDataTypeCode());
            return iClient.executeQuery(iSourceDTO, queryDTO);
        } catch (Exception e) {
            log.error(String.format("SQL executed exception, %s", e.getMessage()));
            throw new RdosDefineException(e.getMessage());
        }

    }

    /**
     * 执行SQL（支持预编译，只需设置preFields预编译字段）--获取源数据信息
     *
     * @param datasourceDTO 数据源工具参数
     * @return 结果
     */
    public static List<JdbcSqlMetadataInfoDTO> getMetadataByExecuteQuery(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        /**
         *  gp6 不支持动态切换库
         *
         */
        JSONObject dataJsonMap = datasourceDTO.getDataJsonMap();
        String schema = dataJsonMap.getString("schema");
        if (datasourceDTO.getDataTypeCode() == DataSourceType.GREENPLUM6.getVal()) {
            //先判断schema  是否变化
            String schema1 = datasourceDTO.getSchema();
            if (StringUtils.isNotBlank(schema1) && !schema1.equals(schema)) {
                throw new RdosDefineException("Greenplum6 不支持动态切换库");
            }
        }
        dataJsonMap.put("schema", datasourceDTO.getSchema());
        ISourceDTO iSourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(), datasourceDTO.getDataTypeCode(), datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql(datasourceDTO.getSql()).build();
        if (datasourceDTO.getQueryTimeout() != null && datasourceDTO.getQueryTimeout() > 0) {
            //查询超时时间
            queryDTO.setQueryTimeout(datasourceDTO.getQueryTimeout());
        }
        if (CollectionUtils.isNotEmpty(datasourceDTO.getPreFields())) {
            //设置preFields预编译字段
            queryDTO.setPreFields(datasourceDTO.getPreFields());
        }
        IClient iClient = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        return iClient.executeSqlForMetadataInfo(iSourceDTO, queryDTO);
    }


    /**
     * 获取当前数据源的版本
     *
     * @param datasourceDTO 数据源工具参数
     * @return 数据源版本
     */
    public static String getVersion(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        Integer dataTypeCode = datasourceDTO.getDataTypeCode();
        //FIXME clickhouse和impala  hive 没有字符集的概念
        if (
                DataSourceType.INCEPTOR.getVal().equals(dataTypeCode)
        ) {
            log.warn("[{}]没有版本，返回空字符串", DataSourceTypeEnum.valOf(dataTypeCode).getDataType());
            return "";
        }

        ISourceDTO iSourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(), datasourceDTO.getDataTypeCode(), datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        IClient iClient = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        return iClient.getVersion(iSourceDTO);
    }

    //---------------------------------- kafka相关操作 ----------------------------------

    /**
     * 测试kafka连接
     *
     * @param kafkaDTO kafka数据源DTO
     * @return 连接成功true / 连接失败false
     */
    public static Boolean testConForKafka(@NotNull KafkaDTO kafkaDTO) {
        //设置插件包路径
        if (kafkaDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(kafkaDTO.getDataSourcePluginPath());
        }
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(kafkaDTO.getDataJsonMap(), kafkaDTO.getDataTypeCode(), null, null, null);
        IKafka kafka = ClientCache.getKafka(sourceDTO.getSourceType());
        return kafka.testCon(sourceDTO);
    }

    /**
     * 查询Topic数据
     *
     * @param kafkaDTO kafka数据源DTO
     */
    public static List<String> getTopicData(@NotNull KafkaDTO kafkaDTO) {
        //设置插件包路径
        if (kafkaDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(kafkaDTO.getDataSourcePluginPath());
        }
        List<Object> records = new ArrayList<>();
        Future<List<Object>> future = executor.submit(() -> {
            ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(kafkaDTO.getDataJsonMap(), kafkaDTO.getDataTypeCode(), null, Maps.newHashMap());
            SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder().tableName(kafkaDTO.getTopic()).build();
            List<List<Object>> preview;
            try {
                preview = ClientCache.getKafka(DataSourceType.KAFKA.getVal()).getPreview(sourceDTO, sqlQueryDTO, kafkaDTO.getPreviewModel());
            } catch (Exception e) {
                throw new DatasourceDefException(String.format("Kafka 预览异常,Caused by: %s", e.getMessage()), e);
            }
            return preview.get(0);
        });
        try {
            records = future.get(5000, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            future.cancel(true);
        }
        return records.stream().map(Object::toString).collect(Collectors.toList());
    }

    /**
     * 查询Topic数据
     *
     * @param kafkaDTO kafka数据源DTO
     */
//    public static List<String> getTopicData(@NotNull KafkaDTO kafkaDTO,String maxpoolrecords) {
//        //设置插件包路径
//        if (kafkaDTO.isInitDataSourcePluginPath()) {
//            initDataSourcePluginPath(kafkaDTO.getDataSourcePluginPath());
//        }
//        List<Object> records = new ArrayList<>();
//        Future<List<Object>> future = executor.submit(() -> {
//            ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(kafkaDTO.getDataJsonMap(), kafkaDTO.getDataTypeCode(), null, Maps.newHashMap());
//            SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder().tableName(kafkaDTO.getTopic()).build();
//            List<List<Object>> preview;
//            try {
//                preview = ClientCache.getKafka(DataSourceType.KAFKA.getVal()).getPreview(sourceDTO, sqlQueryDTO, kafkaDTO.getPreviewModel(),maxpoolrecords);
//            } catch (Exception e) {
//                throw new DatasourceDefException(String.format("Kafka 预览异常,Caused by: %s", e.getMessage()), e);
//            }
//            return preview.get(0);
//        });
//        try {
//            records = future.get(5000, TimeUnit.MILLISECONDS);
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            future.cancel(true);
//        }
//        return records.stream().map(Object::toString).collect(Collectors.toList());
//    }
    public static List<Object> getTopicDataByPartition(@NotNull KafkaDTO kafkaDTO, String maxpoolrecords) {
        //设置插件包路径
        if (kafkaDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(kafkaDTO.getDataSourcePluginPath());
        }
        List<Object> records = new ArrayList<>();
        Future<List<Object>> future = executor.submit(() -> {
            ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(kafkaDTO.getDataJsonMap(), kafkaDTO.getDataTypeCode(), null, Maps.newHashMap());
            List<List<Object>> preview;
            try {
                preview = ClientCache.getKafka(DataSourceType.KAFKA.getVal()).getRecordsFromKafkaByStatistics(sourceDTO, kafkaDTO.getPartitions(), kafkaDTO.getTopic(), kafkaDTO.getPreviewModel(), maxpoolrecords);
            } catch (Exception e) {
                throw new DatasourceDefException(String.format("Kafka 预览异常,Caused by: %s", e.getMessage()), e);
            }
            return preview.get(0);
        });
        try {
            records = future.get(5000, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            future.cancel(true);
        }
        return records;
    }

    /**
     * 获取Kafka所有Topic信息
     *
     * @param kafkaDTO kafka数据源DTO
     * @return topic列表
     */
    public static List<String> getKafkaTopics(@NotNull KafkaDTO kafkaDTO) {
        //设置插件包路径
        if (kafkaDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(kafkaDTO.getDataSourcePluginPath());
        }
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(kafkaDTO.getDataJsonMap(), kafkaDTO.getDataTypeCode(), null, null, null);
        IKafka kafka = ClientCache.getKafka(sourceDTO.getSourceType());
        return kafka.getTopicList(sourceDTO);
    }

    /**
     * 获取所有 kafka Brokers 的地址
     *
     * @param kafkaDTO kafka数据源DTO
     * @return 所有 Brokers 的地址
     */
    public static String getAllBrokersAddress(@NotNull KafkaDTO kafkaDTO) {
        //设置插件包路径
        if (kafkaDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(kafkaDTO.getDataSourcePluginPath());
        }
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(kafkaDTO.getDataJsonMap(), kafkaDTO.getDataTypeCode(), null, null, null);
        IKafka kafka = ClientCache.getKafka(sourceDTO.getSourceType());
        return kafka.getAllBrokersAddress(sourceDTO);
    }


    /**
     * 获取kafka指定topic下的分区信息
     *
     * @param kafkaDTO kafka数据源DTO
     * @return kafka分区信息传输类列表
     */
    public static List<KafkaPartitionDTO> getTopicPartitions(@NotNull KafkaDTO kafkaDTO) {
        //设置插件包路径
        if (kafkaDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(kafkaDTO.getDataSourcePluginPath());
        }
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(kafkaDTO.getDataJsonMap(), kafkaDTO.getDataTypeCode(), null, null, null);
        IKafka kafka = ClientCache.getKafka(kafkaDTO.getDataTypeCode());
        return kafka.getTopicPartitions(sourceDTO, kafkaDTO.getTopic());
    }

    /**
     * 获取特定 Topic 所有分区的偏移量
     *
     * @param kafkaDTO kafka数据源DTO
     * @return kafka分区信息传输类列表
     */
    public static List<KafkaPartitionDTO> getOffset(@NotNull KafkaDTO kafkaDTO) {
        //设置插件包路径
        if (kafkaDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(kafkaDTO.getDataSourcePluginPath());
        }
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(kafkaDTO.getDataJsonMap(), kafkaDTO.getDataTypeCode(), null, null, null);
        IKafka kafka = ClientCache.getKafka(kafkaDTO.getDataTypeCode());
        return kafka.getOffset(sourceDTO, kafkaDTO.getTopic());
    }


    /**
     * 从kafka 中消费数据
     *
     * @param kafkaDTO kafka数据源DTO
     * @return kafka数据
     */
    public static List<KafkaPartitionDTO> consumeData(@NotNull KafkaDTO kafkaDTO) {
        //设置插件包路径
        if (kafkaDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(kafkaDTO.getDataSourcePluginPath());
        }
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(kafkaDTO.getDataJsonMap(), kafkaDTO.getDataTypeCode(), null, null, null);
        IKafka kafka = ClientCache.getKafka(kafkaDTO.getDataTypeCode());
        return kafka.consumeData(sourceDTO, kafkaDTO.getTopic(),
                kafkaDTO.getCollectNum() == null ? 100 : kafkaDTO.getCollectNum(),
                kafkaDTO.getOffsetReset(), kafkaDTO.getTimestampOffset(), kafkaDTO.getMaxTimeWait());
    }

    /**
     * 获取 kafka 消费者组列表
     *
     * @param kafkaDTO kafka数据源DTO
     * @return 消费者组列表
     */
    public static List<KafkaPartitionDTO> listConsumerGroup(@NotNull KafkaDTO kafkaDTO) {
        //设置插件包路径
        if (kafkaDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(kafkaDTO.getDataSourcePluginPath());
        }
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(kafkaDTO.getDataJsonMap(), kafkaDTO.getDataTypeCode(), null, null, null);
        IKafka kafka = ClientCache.getKafka(kafkaDTO.getDataTypeCode());
        return kafka.listConsumerGroup(sourceDTO);
    }

    /**
     * 获取指定topic下的所有的消费者组
     *
     * @param kafkaDTO kafka数据源DTO
     * @return 消费者组列表
     */
    public static List<KafkaPartitionDTO> listConsumerGroupByTopic(@NotNull KafkaDTO kafkaDTO) {
        //设置插件包路径
        if (kafkaDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(kafkaDTO.getDataSourcePluginPath());
        }
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(kafkaDTO.getDataJsonMap(), kafkaDTO.getDataTypeCode(), null, null, null);
        IKafka kafka = ClientCache.getKafka(kafkaDTO.getDataTypeCode());
        return kafka.listConsumerGroupByTopic(sourceDTO, kafkaDTO.getTopic());
    }

    /**
     * 获取 kafka 消费者组详细信息
     *
     * @param kafkaDTO kafka数据源DTO
     * @return 消费者组详细信息
     */
    public static List<KafkaPartitionDTO> getGroupInfoByGroupId(@NotNull KafkaDTO kafkaDTO) {
        //设置插件包路径
        if (kafkaDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(kafkaDTO.getDataSourcePluginPath());
        }
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(kafkaDTO.getDataJsonMap(), kafkaDTO.getDataTypeCode(), null, null, null);
        IKafka kafka = ClientCache.getKafka(kafkaDTO.getDataTypeCode());
        return kafka.getGroupInfoByGroupId(sourceDTO, kafkaDTO.getGroupId());
    }

    /**
     * 获取 kafka 指定topic 下消费者组详细信息
     *
     * @param kafkaDTO kafka数据源DTO
     * @return 消费者组详细信息
     */
    public static List<KafkaPartitionDTO> getGroupInfoByTopic(@NotNull KafkaDTO kafkaDTO) {
        //设置插件包路径
        if (kafkaDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(kafkaDTO.getDataSourcePluginPath());
        }
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(kafkaDTO.getDataJsonMap(), kafkaDTO.getDataTypeCode(), null, null, null);
        IKafka kafka = ClientCache.getKafka(kafkaDTO.getDataTypeCode());
        return kafka.getGroupInfoByTopic(sourceDTO, kafkaDTO.getTopic());
    }


    /**
     * 获取 kafka 指定topic下指定消费者组详细信息
     *
     * @param kafkaDTO kafka数据源DTO
     * @return 消费者组详细信息
     */
    public static List<KafkaPartitionDTO> getGroupInfoByGroupIdAndTopic(@NotNull KafkaDTO kafkaDTO) {
        //设置插件包路径
        if (kafkaDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(kafkaDTO.getDataSourcePluginPath());
        }
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(kafkaDTO.getDataJsonMap(), kafkaDTO.getDataTypeCode(), null, null, null);
        IKafka kafka = ClientCache.getKafka(kafkaDTO.getDataTypeCode());
        return kafka.getGroupInfoByGroupIdAndTopic(sourceDTO, kafkaDTO.getGroupId(), kafkaDTO.getTopic());
    }

    //---------------------------------- kerberos相关操作 ----------------------------------
    public static final String SASL_KERBEROS_SERVICE_NAME = "sasl.kerberos.service.name";
    /**
     * Kerberos 文件上传的时间戳
     */
    public static final String KERBEROS_FILE_TIMESTAMP = "kerberosFileTimestamp";
    public static final String OPEN_KERBEROS = "openKerberos";
    public static final String KERBEROS_FILE = "kerberosFile";
    public static final String UPLOADFILE_CONFIG = "uploadFilePath";

    public static Map<String, String> initKafkaKerberos(String serviceName) {
        Map<String, String> kafkaSettings = new HashMap<>();
        kafkaSettings.put("security.protocol", "SASL_PLAINTEXT");
        kafkaSettings.put("sasl.mechansim", "GSSAPI");
        kafkaSettings.put(SASL_KERBEROS_SERVICE_NAME, serviceName);
        return kafkaSettings;
    }

    /**
     * 获取 Kerberos 参数信息
     *
     * @param base64Str
     * @param check
     * @return
     */
    public static JSONObject getOriginKerberosConfig(String base64Str, boolean check) {
        JSONObject originDataJson = getDataSourceJson(base64Str);
        return getOriginKerberosConfig(originDataJson, check);
    }

    /**
     * 获取 Kerberos 参数
     *
     * @param dataJson
     * @param check
     * @return
     */
    public static JSONObject getOriginKerberosConfig(JSONObject dataJson, boolean check) {
        JSONObject kerberosConfig = dataJson.getJSONObject(KERBEROS_CONFIG);
        if (check && kerberosConfig == null) {
            throw new RdosDefineException("kerberos配置缺失");
        }
        return kerberosConfig;
    }

    public static void getOriginKerberosConfig(JSONObject dataJson, String sourceDataJson) {
        if (Strings.isBlank(sourceDataJson)) {
            return;
        }
        JSONObject originDataJson = getDataSourceJson(sourceDataJson);
        dataJson.put(OPEN_KERBEROS, originDataJson.get(OPEN_KERBEROS));
        dataJson.put(KERBEROS_FILE, originDataJson.getJSONObject(KERBEROS_FILE));
    }

    /*
     * <AUTHOR> 获取kafka kerberos文件
     */
    public static JSONObject getOriginKerberosFile(String sourceDataJson) {
        if (Strings.isBlank(sourceDataJson)) {
            return null;
        }
        JSONObject originDataJson = getDataSourceJson(sourceDataJson);
        JSONObject jsonObject = originDataJson.getJSONObject(KERBEROS_FILE);
        return jsonObject;
    }

    /**
     * 解析 dataJson 参数
     *
     * @param base64Str
     * @return
     */
    public static JSONObject getDataSourceJson(String base64Str) {
        if (Strings.isNullOrEmpty(base64Str)) {
            return new JSONObject();
        }
        try {
            boolean isJson = JSONValidator.from(base64Str).validate();
            if (isJson) {
                // 是json字符串
                return JSONObject.parseObject(base64Str);
            } else {
                // 非json字符串
                return JSONObject.parseObject(Base64Util.baseDecode(base64Str));
            }
        } catch (Exception e) {
            log.error("数据源信息解码异常", e);
            throw new RdosDefineException("数据源信息解码异常", e);
        }
    }

    /**
     * 设置openKerberos开启属性
     *
     * @param dataJson
     * @param open
     */
    public static void setOpenKerberos(JSONObject dataJson, Boolean open) {
        dataJson.put(OPEN_KERBEROS, open);
    }

    /**
     * 设置ssl文件属性
     *
     * @param dataJson
     * @param fileName
     */
    public static void setKerberosFile(JSONObject dataJson, String fileName) {
        Map<String, String> kerberosFile = new HashMap<>();
        kerberosFile.put("name", fileName);
        kerberosFile.put("modifyTime", Timestamp.valueOf(LocalDateTime.now()).toString());
        dataJson.put(KERBEROS_FILE, kerberosFile);
        dataJson.put(KERBEROS_FILE_TIMESTAMP, new Timestamp(System.currentTimeMillis()));
    }

    /**
     * 获取topic列表
     *
     * @param datasourceDTO
     * @param confMap
     * @param localKerberosPath
     * @return
     */

    public static List<String> getTopicList(@NotNull DatasourceDTO datasourceDTO,
                                            Map<String, Object> confMap, String localKerberosPath) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        return client.getTopicList(iSourceDTO);
    }


    /**
     * 根据topic获取group
     *
     * @param datasourceDTO
     * @param confMap
     * @param localKerberosPath
     * @param topicName
     * @return
     */
    public static List<KafkaConsumerDTO> getGroupInfoByTopic(@NotNull DatasourceDTO datasourceDTO,
                                                             Map<String, Object> confMap, String localKerberosPath, String topicName) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        return client.getGroupInfoByTopic(iSourceDTO, topicName);
    }

    /**
     * 获取topic详情列表
     *
     * @param datasourceDTO
     * @param confMap
     * @param localKerberosPath
     * @return
     */
    public static List<TopIcInfoDTO> getTopicInfoList(@NotNull DatasourceDTO datasourceDTO,
                                                      Map<String, Object> confMap, String localKerberosPath) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        return client.getTopicInfoList(iSourceDTO);
    }

    /**
     * 获取broker详情列表
     *
     * @param datasourceDTO
     * @param confMap
     * @param localKerberosPath
     * @return
     */
    public static List<BrokersDTO> getBrokerInfoList(@NotNull DatasourceDTO datasourceDTO,
                                                     Map<String, Object> confMap, String localKerberosPath) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        return client.getAllBrokersInfoList(iSourceDTO);

    }

    public static JSONObject getAllActiveTopicsTree(@NotNull DatasourceDTO datasourceDTO,
                                                    Map<String, Object> confMap, String localKerberosPath) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        return client.getAllActiveTopicsTree(iSourceDTO);

    }

    /*
     * <AUTHOR>
     * @Param  * @param datasourceDTO
     * @param confMap
     * @param localKerberosPath
     * @param topicName
     * @return java.util.List<com.alibaba.fastjson.JSONObject>
     * @Date 2023/2/13 11:45
     * @Version V1.0
     *  根据topic获取consumer的详细信息
     */
    public static List<JSONObject> getConsumerInfoByTopICName(@NotNull DatasourceDTO datasourceDTO,
                                                              Map<String, Object> confMap, String localKerberosPath, String topicName) {
        List<JSONObject> result = new ArrayList<>();
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        List<String> list = client.listConsumerGroupByTopic(iSourceDTO, topicName);

        for (String s : list) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("groupId", s);
            jsonObject.put("topic", topicName);
            long lag = 0l;
            List<KafkaConsumerDTO> groupInfoByGroupIdAndTopic = client.getGroupInfoByGroupIdAndTopic(iSourceDTO, s, topicName);
            for (KafkaConsumerDTO kafkaConsumerDTO : groupInfoByGroupIdAndTopic) {
                lag = lag + kafkaConsumerDTO.getLag();

            }
            jsonObject.put("lag", lag);
            String consumerStatus = client.getConsumerStatus(iSourceDTO, s);
            jsonObject.put("status", consumerStatus);
//            jsonObject.put("consumersOffsets",groupInfoByGroupIdAndTopic);
            result.add(jsonObject);
        }
        return result;

    }

    /*
     * <AUTHOR>
     * @Param  * @param datasourceDTO
     * @param confMap
     * @param localKerberosPath
     * @param topicName
     * @param groupId
     * @return java.util.List<com.dtstack.dtcenter.loader.dto.KafkaConsumerDTO>
     * @Date 2023/2/13 11:46
     * @Version V1.0
     *  根据topic和group 获取offset信息用于监控展示
     */
    public static List<KafkaConsumerDTO> getOffsetInfoByTopICAndGroupId(@NotNull DatasourceDTO datasourceDTO,
                                                                        Map<String, Object> confMap, String localKerberosPath, String topicName, String groupId) {
        List<JSONObject> result = new ArrayList<>();
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        List<KafkaConsumerDTO> groupInfoByGroupIdAndTopic = client.getGroupInfoByGroupIdAndTopic(iSourceDTO, groupId, topicName);
        return groupInfoByGroupIdAndTopic;
    }

    public static Integer getBrokerSpread(@NotNull DatasourceDTO datasourceDTO,
                                          Map<String, Object> confMap, String localKerberosPath, String topic) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        return client.getBrokerSpread(iSourceDTO, topic);

    }

    //获取broker的skewed
    public static Integer getBrokerSkewed(@NotNull DatasourceDTO datasourceDTO,
                                          Map<String, Object> confMap, String localKerberosPath, String topic) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        return client.getBrokerSkewed(iSourceDTO, topic);

    }

    //获取kafka broker leader skewed
    public static Integer getBrokerLeaderSkewed(@NotNull DatasourceDTO datasourceDTO,
                                                Map<String, Object> confMap, String localKerberosPath, String topic) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        return client.getBrokerLeaderSkewed(iSourceDTO, topic);

    }

    //获取kafka 的broker版本
    public static String getBrokerKafkaVersion(@NotNull DatasourceDTO datasourceDTO,
                                               Map<String, Object> confMap, String localKerberosPath, String host, Integer port, String brokerId) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        return client.getBrokerKafkaVersion(host, port, brokerId);
    }

    //创建topic
    public static Boolean createTopic(@NotNull DatasourceDTO datasourceDTO,
                                      Map<String, Object> confMap, String localKerberosPath, KafkaTopicDTO kafkaTopicDTO) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        return client.createTopicFromBroker(iSourceDTO, kafkaTopicDTO);
    }

    //创建topic分区
    public static Boolean createTopicPartitions(@NotNull DatasourceDTO datasourceDTO,
                                                Map<String, Object> confMap, String localKerberosPath, String topicName, Integer partitions) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        return client.createTopicPartitions(iSourceDTO, topicName, partitions);
    }

    //删除topic
    public static Boolean deleteTopic(@NotNull DatasourceDTO datasourceDTO,
                                      Map<String, Object> confMap, String localKerberosPath, String topicName) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        return client.deleteTOpic(iSourceDTO, topicName);
    }

    /**
     * 获取broker名字列表
     *
     * @param datasourceDTO
     * @param confMap
     * @param localKerberosPath
     * @return
     */
    public static List<String> getBrokerList(@NotNull DatasourceDTO datasourceDTO,
                                             Map<String, Object> confMap, String localKerberosPath) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        String allBrokersAddress = client.getAllBrokersAddress(iSourceDTO);
        String[] split = allBrokersAddress.split(",");
        List<String> strings = Arrays.asList(split);
        return strings;
    }


    /**
     * 获取consumer名字列表
     *
     * @param datasourceDTO
     * @param confMap
     * @param localKerberosPath
     * @return
     */
    public static List<String> getConsumerList(@NotNull DatasourceDTO datasourceDTO,
                                               Map<String, Object> confMap, String localKerberosPath) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        List<String> list = client.listConsumerGroup(iSourceDTO);
        return list;
    }

    /**
     * 获取consumergroup列表
     *
     * @param datasourceDTO
     * @param confMap
     * @param localKerberosPath
     * @return
     */
    public static List<ConsumerInfoDTO> getConsumerGroupList(@NotNull DatasourceDTO datasourceDTO,
                                                             Map<String, Object> confMap, String localKerberosPath) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        List<ConsumerInfoDTO> consumerGroupList = client.getConsumerInfoList(iSourceDTO);
        return consumerGroupList;

    }

    //获取topic详细信息
    public static List<TopIcMetaInfoDTO> getTopIcMetaByTopIc(@NotNull DatasourceDTO datasourceDTO,
                                                             Map<String, Object> confMap, String localKerberosPath, String topIcName) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        List<TopIcMetaInfoDTO> topIcMetaByTopIc = client.getTopIcMetaByTopIc(iSourceDTO, topIcName);
        return topIcMetaByTopIc;
    }

    //根据group获取topic
    public static JSONObject listTopicInfoByGroupId(@NotNull DatasourceDTO datasourceDTO,
                                                    Map<String, Object> confMap, String localKerberosPath, String groupId) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        JSONObject objects = client.listTopicInfoByGroupId(iSourceDTO, groupId);
        return objects;
    }

    //获取broker cpu利用率
    public static String getBrokerCpuUse(@NotNull DatasourceDTO datasourceDTO,
                                         Map<String, Object> confMap, String localKerberosPath, String host, Integer port) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());

        String brokerCpuUse = client.getBrokerCpuUse(iSourceDTO, host, port);
        return brokerCpuUse;

    }

    //获取broker内存利用率
    public static String getBrokerMemoryUse(@NotNull DatasourceDTO datasourceDTO,
                                            Map<String, Object> confMap, String localKerberosPath, String host, Integer port) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());

        String brokerMemoryUse = client.getBrokerMemoryUse(iSourceDTO, host, port);
        return brokerMemoryUse;

    }

    //获取broker利用率百分比
    public static String getBrokerMemoryUsePercent(@NotNull DatasourceDTO datasourceDTO,
                                                   Map<String, Object> confMap, String localKerberosPath, String host, Integer port) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        String brokerMemoryUse = client.getBrokerMemoryUsePercent(iSourceDTO, host, port);
        return brokerMemoryUse;

    }

    //获取OS Memory
    public static double getOSMemory(@NotNull DatasourceDTO datasourceDTO,
                                     Map<String, Object> confMap, String localKerberosPath) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        double osMemory = client.getOSMemory(iSourceDTO);
        return osMemory;

    }


    //获取CPU利用率
    public static double getCpuUsed(@NotNull DatasourceDTO datasourceDTO,
                                    Map<String, Object> confMap, String localKerberosPath) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        double cpuUsed = client.getCpuUsed(iSourceDTO);

        return cpuUsed;

    }

    //获取broker指标详情
    public static List<KpiInfoDTO> getBrokerKpiInfos(@NotNull DatasourceDTO datasourceDTO,
                                                     Map<String, Object> confMap, String localKerberosPath) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        List<KpiInfoDTO> brokerKpiInfos = client.getBrokerKpiInfos(iSourceDTO);

        return brokerKpiInfos;

    }

    //获取kafka topic offset
    public static List<OffsetInfoDTO> getTopicOffset(@NotNull DatasourceDTO datasourceDTO,
                                                     Map<String, Object> confMap, String localKerberosPath, String topicName, String groupName) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        List<OffsetInfoDTO> getTopicOffset = client.getTopicOffset(iSourceDTO, topicName, groupName);

        return getTopicOffset;

    }

    //获取topic的logsize
    public static long getLogSizeByTopic(@NotNull DatasourceDTO datasourceDTO,
                                         Map<String, Object> confMap, String localKerberosPath, String topicName) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        long logSizeByTopic = client.getLogSizeByTopic(iSourceDTO, topicName);
        return logSizeByTopic;

    }

    //获取kafka topic 生产消息统计
    public static long getKafkaProducerLogSizeByTopic(@NotNull DatasourceDTO datasourceDTO,
                                                      Map<String, Object> confMap, String localKerberosPath, String topicName) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        long logSizeByTopic = client.getKafkaProducerLogSizeByTopic(iSourceDTO, topicName);
        return logSizeByTopic;

    }


    //获取topic logsize
    public static long getCapacityByTopic(@NotNull DatasourceDTO datasourceDTO,
                                          Map<String, Object> confMap, String localKerberosPath, String topicName) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        long capacityByTopic = client.getCapacityByTopic(iSourceDTO, topicName);
        return capacityByTopic;

    }

    public static long getByteInByTopic(@NotNull DatasourceDTO datasourceDTO,
                                        Map<String, Object> confMap, String localKerberosPath, String topicName) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        long byteInByTopic = client.getByteInByTopic(iSourceDTO, topicName);
        return byteInByTopic;

    }

    public static long getByteOutByTopic(@NotNull DatasourceDTO datasourceDTO,
                                         Map<String, Object> confMap, String localKerberosPath, String topicName) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        long byteOutByTopic = client.getByteOutByTopic(iSourceDTO, topicName);
        return byteOutByTopic;

    }


    public static Map<String, KafkaMonitorDTO> getTopicMonitor(@NotNull DatasourceDTO datasourceDTO,
                                                               Map<String, Object> confMap, String localKerberosPath, String topic) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        Map<String, KafkaMonitorDTO> topicMonitor = client.getTopicMonitor(iSourceDTO, topic);
        return topicMonitor;

    }

    public static List<OffsetInfoDTO> getTopicMessageStatistics(@NotNull DatasourceDTO datasourceDTO,
                                                                Map<String, Object> confMap, String localKerberosPath, String topic) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        List<String> groupInfoByTopic = client.listConsumerGroupByTopic(sourceDTO, topic);
        List<OffsetInfoDTO> result = new ArrayList<>();
        for (String kafkaConsumerDTO : groupInfoByTopic) {
            OffsetInfoDTO topicMessageStatistics = client.getTopicMessageStatistics(iSourceDTO, topic, kafkaConsumerDTO);
            result.add(topicMessageStatistics);
        }
        return result;

    }

    public static Map<String, KafkaMonitorDTO> getOnlineAllBrokersMBean(@NotNull DatasourceDTO datasourceDTO,
                                                                        Map<String, Object> confMap, String localKerberosPath, String host, Integer port) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        IKafka client = ClientCache.getKafka(typeEnum.getVal());
        Map<String, KafkaMonitorDTO> onlineAllBrokersMBean = client.getOnlineAllBrokersMBean(host, port);
        return onlineAllBrokersMBean;

    }


    /**
     * 设置前台传入的principals
     *
     * @param dataJson
     * @param confMap
     */
    public static void setPrincipals(JSONObject dataJson, Map<String, Object> confMap) {
        //principal 键
        String principal = dataJson.getString(FormNames.PRINCIPAL);
        if (StringUtils.isNotBlank(principal)) {
            confMap.put(HadoopConfTool.PRINCIPAL, principal);
        }
        //Hbase master kerberos Principal
        String hbaseMasterPrincipal = dataJson.getString(FormNames.HBASE_MASTER_PRINCIPAL);
        if (StringUtils.isNotBlank(hbaseMasterPrincipal)) {
            confMap.put(HadoopConfTool.HBASE_MASTER_PRINCIPAL, hbaseMasterPrincipal);
        }
        //Hbase region kerberos Principal
        String hbaseRegionserverPrincipal = dataJson.getString(FormNames.HBASE_REGION_PRINCIPAL);
        if (StringUtils.isNotBlank(hbaseRegionserverPrincipal)) {
            confMap.put(HadoopConfTool.HBASE_REGION_PRINCIPAL, hbaseRegionserverPrincipal);
        }
    }


    public static ISourceDTO parseParams(DatasourceDTO datasourceDTO,
                                         Map<String, Object> confMap, String localKerberosPath, DataSourceTypeEnum typeEnum, ISourceDTO sourceDTO) {
        typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());



        // 替换相对绝对路径
        Map<String, Object> tempConfMap = null;
        if (MapUtils.isNotEmpty(confMap)) {
            tempConfMap = Maps.newHashMap(confMap);
            IKerberos kerberos = ClientCache.getKerberos(typeEnum.getVal());

//            kerberos.prepareKerberosForConnect(tempConfMap, localKerberosPath);
        }
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        // 获取topicList
        sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), tempConfMap, Maps.newHashMap());
        return sourceDTO;

    }

    //hdfs 相关操作
    //hdfs distcp
    public static String startDistCp(@NotNull DatasourceDTO datasourceDTO,
                                     Map<String, Object> kerberosConfig, String localKerberosPath, DsgDistCPParams dsgDistCPParams, String resourceJarPath, String callBackUrls, String logOutPutPath) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), kerberosConfig, Maps.newHashMap());
        IHdfsFile hdfs = ClientCache.getHdfs(typeEnum.getVal());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, kerberosConfig, localKerberosPath, typeEnum, sourceDTO);
        String s = hdfs.startDistcp(iSourceDTO, dsgDistCPParams, resourceJarPath, callBackUrls, logOutPutPath);
        return s;

    }

    //hdfs stop MapReduce任务
    public static Boolean stopMrJob(@NotNull DatasourceDTO datasourceDTO,
                                    Map<String, Object> kerberosConfig, String localKerberosPath, String jobId, String ip, Integer port) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), kerberosConfig, Maps.newHashMap());
        IHdfsFile hdfs = ClientCache.getHdfs(typeEnum.getVal());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, kerberosConfig, localKerberosPath, typeEnum, sourceDTO);
        Boolean aBoolean = hdfs.stopMrJob(iSourceDTO, jobId, ip, port);
        return aBoolean;

    }

    //hdfs HA获取高可用节点信息
    public static String getActiveNameNode(@NotNull DatasourceDTO datasourceDTO,
                                           Map<String, Object> confMap, String localKerberosPath) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        IHdfsFile hdfs = ClientCache.getHdfs(typeEnum.getVal());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        String s = hdfs.getActiveNameNode(iSourceDTO);
        return s;

    }

    //hbase相关操作
    //获取表命名空间
    public static List<JSONObject> getAllNameSpaces(@NotNull DatasourceDTO datasourceDTO,
                                                    Map<String, Object> confMap, String localKerberosPath) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        IHbase hbase = ClientCache.getHbase(typeEnum.getVal());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        List<JSONObject> result = hbase.getAllNameSpaces(iSourceDTO);
        return result;
    }

    public static List<JSONObject> getNameSpacesByName(@NotNull DatasourceDTO datasourceDTO,
                                                       Map<String, Object> confMap, String localKerberosPath, String namespaceName) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        IHbase hbase = ClientCache.getHbase(typeEnum.getVal());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        List<JSONObject> result = hbase.getNameSpacesByName(iSourceDTO, namespaceName);
        return result;
    }

    //创建命名空间
    public static Boolean createNameSpaces(@NotNull DatasourceDTO datasourceDTO,
                                           Map<String, Object> confMap, String localKerberosPath, List<JSONObject> namespaceDescriptors) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        IHbase hbase = ClientCache.getHbase(typeEnum.getVal());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        Boolean result = hbase.createNameSpaces(iSourceDTO, namespaceDescriptors);
        return result;
    }

    //获取表列表信息
    public static List<Object> getAllTableDescriptor(@NotNull DatasourceDTO datasourceDTO,
                                                     Map<String, Object> confMap, String localKerberosPath) {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        IHbase hbase = ClientCache.getHbase(typeEnum.getVal());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        List<Object> allTableDescriptor = hbase.getAllTableDescriptor(iSourceDTO);
        return allTableDescriptor;
    }


    //获取表信息
    public static List<Object> getTableDescriptorByTableName(@NotNull DatasourceDTO datasourceDTO,
                                                             Map<String, Object> confMap, String localKerberosPath, String tableName) throws Exception {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        IHbase hbase = ClientCache.getHbase(typeEnum.getVal());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        List<Object> allTableDescriptor = hbase.getTableDescriptorByTableName(iSourceDTO, tableName);
        return allTableDescriptor;
    }

    //获取表信息
    public static Boolean tableExist(@NotNull DatasourceDTO datasourceDTO,
                                     Map<String, Object> confMap, String localKerberosPath, String tableName) throws Exception {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        IHbase hbase = ClientCache.getHbase(typeEnum.getVal());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        Boolean aBoolean = hbase.tableExist(iSourceDTO, tableName);
        return aBoolean;
    }

    //根据表信息创建表
    public static Boolean createTableDescriptor(@NotNull DatasourceDTO datasourceDTO,
                                                Map<String, Object> confMap, List<Object> tableDescriptors, String localKerberosPath) throws Exception {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        IHbase hbase = ClientCache.getHbase(typeEnum.getVal());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        Boolean tableDescriptor = hbase.createTableDescriptor(iSourceDTO, tableDescriptors);
        return tableDescriptor;
    }

    //创建表快照
    public static Boolean createTableSnapshot(@NotNull DatasourceDTO datasourceDTO,
                                              Map<String, Object> confMap, String tableName, String snapShotName, String localKerberosPath) throws Exception {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        IHbase hbase = ClientCache.getHbase(typeEnum.getVal());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        Boolean tableDescriptor = hbase.createTableSnapshot(iSourceDTO, tableName, snapShotName);
        return tableDescriptor;
    }

    //导出快照
    public static String exportSnapshot(@NotNull DatasourceDTO datasourceDTO,
                                        Map<String, Object> confMap, String snapshotName, String hbaseExportPath, String localKerberosPath, String resourceJarPath
            , String callbackUrl, String logOutPutPath) throws Exception {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        IHbase hbase = ClientCache.getHbase(typeEnum.getVal());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        String jobId = hbase.exportSnapshot(iSourceDTO, snapshotName, hbaseExportPath, resourceJarPath, callbackUrl, logOutPutPath);
        return jobId;
    }

    //恢复快照
    public static Boolean doRestoreHbase(@NotNull DatasourceDTO datasourceDTO,
                                         Map<String, Object> confMap, String tableName, String snapShotName, String localKerberosPath) throws Exception {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        IHbase hbase = ClientCache.getHbase(typeEnum.getVal());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        Boolean aBoolean = hbase.doRestoreHbase(iSourceDTO, tableName, snapShotName);
        return aBoolean;
    }

    //删除快照
    public static Boolean deleteHbaseSnap(@NotNull DatasourceDTO datasourceDTO,
                                          Map<String, Object> confMap, String snapShotName, String localKerberosPath) throws Exception {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        IHbase hbase = ClientCache.getHbase(typeEnum.getVal());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        Boolean aBoolean = hbase.deleteHbaseSnap(iSourceDTO, snapShotName);
        return aBoolean;
    }

    public static String exportData(@NotNull DatasourceDTO datasourceDTO,
                                    Map<String, Object> confMap, String localKerberosPath, String tableName, String hbaseExportPath, String resourceJarPath,
                                    String startTime, String endTime, String callBackUrl, String logOutPutPath) throws Exception {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        IHbase hbase = ClientCache.getHbase(typeEnum.getVal());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        String jobId = hbase.exportData(iSourceDTO, tableName, hbaseExportPath, resourceJarPath, startTime, endTime, callBackUrl, logOutPutPath);
        return jobId;
    }

    public static String importData(@NotNull DatasourceDTO datasourceDTO,
                                    Map<String, Object> confMap, String localKerberosPath, String tableName, String hbaseImportPath, String resourceJarPath,
                                    String callBackUrl, String logOutPutPath) throws Exception {
        DataSourceTypeEnum typeEnum = DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), datasourceDTO.getDataVersion());
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(),
                typeEnum.getVal(), confMap, Maps.newHashMap());
        IHbase hbase = ClientCache.getHbase(typeEnum.getVal());
        ISourceDTO iSourceDTO = parseParams(datasourceDTO, confMap, localKerberosPath, typeEnum, sourceDTO);
        String jobId = hbase.importData(iSourceDTO, tableName, hbaseImportPath, resourceJarPath, callBackUrl, logOutPutPath);
        return jobId;
    }

    /**
     * 获取元数据变更
     *
     * @param metadataChangeDTO 元数据变更实体
     * @return 所有schema
     */
    public static List<MetadataChangeReturnVO> metadataChange(MetadataChangeDTO metadataChangeDTO) {
        List<MetadataChangeReturnVO> metadataChangeReturnVOS = new ArrayList<>();
        //获取元数据表集合
        List<MetadataChangeTable> sourceMetadataTableVOS = metadataChangeDTO.getSourceMetadataTableVOS();
        if (CollectionUtils.isEmpty(sourceMetadataTableVOS)) {
            log.info("没有比较的元数据");
            return metadataChangeReturnVOS;
        }
        log.info("开始比较元数据");
        for (MetadataChangeTable sourceMetadataTableVO : sourceMetadataTableVOS) {
            MetadataChangeReturnVO metadataChangeReturnVO = new MetadataChangeReturnVO();
            try {

                metadataChangeReturnVO.setSourceMetadataTableVO(sourceMetadataTableVO);
                //构建数据源工具参数基本信息
                String datasourceInfoId = sourceMetadataTableVO.getDatasourceInfoId();
                DatasourceDTO datasourceDTO = sourceMetadataTableVO.getDatasourceDTO();
                if (datasourceDTO == null) {
                    log.info("DatasourceDTO:{} ", "数据源参数为空");
                    metadataChangeReturnVO.setMsg("数据源参数为空,不能进行比较");
                    break;
                }
                log.info("DatasourceDTO: " + datasourceDTO);
                //设置插件包路径
                if (datasourceDTO.isInitDataSourcePluginPath()) {
                    initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
                }

                //判断表是否存在
                String tableName = sourceMetadataTableVO.getTableName();
                String tableType = sourceMetadataTableVO.getTableType();
                //获取所有表和视图
                log.info("获取所有表和视图开始----------------------------------------");
                List<TableViewDTO> allList = getTableAndViewList(datasourceDTO);
                //所有表
                List<String> allTableList = new ArrayList<>();
                //所有视图
                List<String> allViewList = new ArrayList<>();
                for (TableViewDTO tableViewDTO : allList) {
                    String type = tableViewDTO.getType();
                    if (MetadataConstants.TABLE.equalsIgnoreCase(type)) {
                        allTableList.add(tableViewDTO.getName());
                    }
                    if (MetadataConstants.VIEW.equalsIgnoreCase(type)) {
                        allViewList.add(tableViewDTO.getName());
                    }
                }
                if (MetadataConstants.TABLE.equalsIgnoreCase(tableType)) {
                    if (!allTableList.contains(tableName)) {
                        metadataChangeReturnVO.setOpt(MetadataConstants.DELETE);
                        metadataChangeReturnVO.setSourceMetadataTableVO(sourceMetadataTableVO);
                        String msg = String.format(MetadataConstants.METADATA_TABLE_OR_VIEW_DELETE, datasourceDTO.getDataName(), sourceMetadataTableVO.getTableType(), sourceMetadataTableVO.getTableName());
                        metadataChangeReturnVO.setMsg(msg);
                        metadataChangeReturnVOS.add(metadataChangeReturnVO);
                        break;
                    }
                }
                if (MetadataConstants.VIEW.equalsIgnoreCase(tableType)) {
                    if (!allViewList.contains(tableName)) {
                        metadataChangeReturnVO.setOpt(MetadataConstants.DELETE);
                        metadataChangeReturnVO.setSourceMetadataTableVO(sourceMetadataTableVO);
                        String msg = String.format(MetadataConstants.METADATA_TABLE_OR_VIEW_DELETE, datasourceDTO.getDataName(), sourceMetadataTableVO.getTableType(), sourceMetadataTableVO.getTableName());
                        metadataChangeReturnVO.setMsg(msg);
                        metadataChangeReturnVOS.add(metadataChangeReturnVO);
                        break;
                    }
                }
                //获取元数据是否有变更
                //获取现在表的字段集合
                //真实库中的表名
                datasourceDTO.setTableName(tableName);
                //数据库中的表注释（null和空字符串视为一种值）
                List<MetadataChangeColumn> metadataColumnVOList = sourceMetadataTableVO.getMetadataColumnVOList();
                //获取数据库中表的字段
                List<MetadataChangeColumn> columnsByTable = getColumnsByTable(datasourceDTO, sourceMetadataTableVO.getMetadataTableUuid(), LocalDateTime.now(), tableName, datasourceInfoId);
                //获取现在表的变化
                MetadataChangeReturnVO metadataChangeReturnVO2 = tableChange(metadataChangeReturnVO, sourceMetadataTableVO);
                //比较字段变化
                MetadataChangeReturnVO metadataChangeReturnVO1 = comparisonFieldAttribute(sourceMetadataTableVO, datasourceDTO, columnsByTable, metadataColumnVOList, metadataChangeReturnVO2);
                metadataChangeReturnVOS.add(metadataChangeReturnVO1);

            } catch (Exception e) {
                metadataChangeReturnVO.setMsg("元数据比较变更失败" + e.getMessage());
                log.error("表{}变更失败：{}----------------------------------", sourceMetadataTableVO.getTableName(), e.getMessage());
            }

        }

        return metadataChangeReturnVOS;
    }


    /**
     * 表变化属性
     */
    public static MetadataChangeReturnVO tableChange(MetadataChangeReturnVO metadataChangeReturnVO,
                                                     MetadataChangeTable sourceMetadataTableVO) throws Exception {

        //获取现在表的表注释
        String tableComment = sourceMetadataTableVO.getTableComment();
        DatasourceDTO datasourceDTO = sourceMetadataTableVO.getDatasourceDTO();
        datasourceDTO.setTableName(sourceMetadataTableVO.getTableName());
        String tableMetaComment = getTableMetaComment(datasourceDTO);
        tableMetaComment = tableMetaComment == null ? "" : tableMetaComment;
        boolean flag = false;

        //获取表长度
        Long tableSize = DatasourceUtils.getTableSize(datasourceDTO);
        tableSize = tableSize == null ? 0 : tableSize;

        //获取表行数
        Long tableRows = DatasourceUtils.getTableRows(datasourceDTO);
        tableRows = tableRows == null ? 0 : tableRows;
        List<MetadataAttributeVO> updateMetadata = new ArrayList<>();
        MetadataChangeTable targetMetadataTable = sourceMetadataTableVO;
        //获取现在表的表注释
        if (notEqualsTwoString(tableMetaComment, tableComment)) {
            log.info("获取表{}注释变更的开始----------------------------------", sourceMetadataTableVO.getTableName());
            flag = true;
            targetMetadataTable.setTableComment(tableMetaComment);
            //构建表属性变化对象
            MetadataAttributeVO metadataAttributeVO = createMetadataAttributeVO(MetadataConstants.TABLE,
                    tableComment,
                    tableMetaComment,
                    MetadataConstants.TABLE_COMMENT);
            updateMetadata.add(metadataAttributeVO);
        }
        //获取现在表大小
        Long tableDataLength = sourceMetadataTableVO.getTableDataLength();
        tableDataLength = tableDataLength == null ? 0 : tableDataLength;
        if (!tableSize.equals(tableDataLength)) {
            log.info("获取表{}大小变更的开始----------------------------------", sourceMetadataTableVO.getTableName());
            flag = true;
            targetMetadataTable.setTableDataLength(tableSize);
            //构建表属性变化对象
            MetadataAttributeVO metadataAttributeVO = createMetadataAttributeVO(MetadataConstants.TABLE,
                    tableSize,
                    tableDataLength,
                    MetadataConstants.TABLE_LENGTH);
            updateMetadata.add(metadataAttributeVO);
        }
        //获取现在表行数
        Long tableDataRows = sourceMetadataTableVO.getTableDataRows();
        tableDataRows = tableDataRows == null ? 0 : tableDataRows;
        if (!tableRows.equals(tableDataRows)) {
            log.info("获取表{}行数变更的开始----------------------------------", sourceMetadataTableVO.getTableName());
            flag = true;
            targetMetadataTable.setTableDataRows(tableRows);
            //构建表属性变化对象
            MetadataAttributeVO metadataAttributeVO = createMetadataAttributeVO(MetadataConstants.TABLE,
                    tableRows,
                    tableDataRows,
                    MetadataConstants.TABLE_ROWS);
            updateMetadata.add(metadataAttributeVO);
        }
        if (flag) {
            metadataChangeReturnVO.setTargetMetadataTableVO(targetMetadataTable);
            metadataChangeReturnVO.setOpt(MetadataConstants.UPDATE);
            metadataChangeReturnVO.setUpdateTableMetadata(updateMetadata);
        }
        return metadataChangeReturnVO;
    }

    /**
     * 遍历最新获取到的源库中当前表对应的字段元数据，判断字段类型、长度、精度、字段注释、主键、外键、唯一约束是否发生改变
     * 2、通过addFlag判断当前表是否该加入到需要重新采集的表名集合中去
     *
     * @param tempTableName  当前表元数据
     * @param datasourceDTO  当前数据源数据
     * @param columnsByTable 源库中当前表的字段元数据列表
     * @param columnList     已采集的当前表的字段元数据
     */
    private static MetadataChangeReturnVO comparisonFieldAttribute(
            MetadataChangeTable tempTableName,
            DatasourceDTO datasourceDTO,
            List<MetadataChangeColumn> columnsByTable,
            List<MetadataChangeColumn> columnList,
            MetadataChangeReturnVO metadataChangeReturnVO) throws Exception {


        String tableName = tempTableName.getTableName();
        log.info("获取表{}新增的字段开始----------------------------------", tableName);
        //获取需要插入的字段元数据
        List<MetadataChangeColumn> needToInsert = columnsByTable.stream()
                .filter(b -> columnList.stream().map(MetadataChangeColumn::getColumnName).noneMatch(columnName -> Objects.equals(b.getColumnName(), columnName))).collect(Collectors.toList());
        log.info("获取表{}新增的字段结束,表字段个数{},表字段{}", tableName, needToInsert.size(), needToInsert.stream().map(MetadataChangeColumn::getColumnName).collect(Collectors.toList()));


        log.info("获取表{}删除的字段开始----------------------------------", tableName);
        //获取需要删除的字段元数据
        List<MetadataChangeColumn> finalColumnsByTable = columnsByTable;
        List<MetadataChangeColumn> needToDelete = columnList.stream()
                .filter(b -> finalColumnsByTable.stream().map(MetadataChangeColumn::getColumnName).noneMatch(columnName -> Objects.equals(b.getColumnName(), columnName)))
                .collect(Collectors.toList());
        log.info("获取表{}删除的字段结束,表字段个数{},表字段{}", tableName, needToDelete.size(), needToDelete.stream().map(MetadataChangeColumn::getColumnName).collect(Collectors.toList()));


        log.info("获取表{}变更的字段开始----------------------------------", tableName);
        //key->字段名，value->对应的已采集的当前表的字段元数据
        Map<String, MetadataChangeColumn> datasourceColumnList = columnList.stream().collect(Collectors.toMap(MetadataChangeColumn::getColumnName, Function.identity(), (k1, k2) -> k2));
        List<MetadataAttributeColVO> updateColMetadata = new ArrayList<>();
        for (MetadataChangeColumn metadataColumnDTO : columnsByTable) {
            //字段元数据变动信息
            //已采集的字段元数据
            MetadataChangeColumn datasourceColumnVO = datasourceColumnList.get(metadataColumnDTO.getColumnName());
            Boolean addFlag = false;
            List<MetadataAttributeVO> metadataAttributeVOS = new ArrayList<>();
            if (datasourceColumnVO != null) {
                //字段类型
                if (notEqualsTwoString(metadataColumnDTO.getColumnType(), datasourceColumnVO.getColumnType())) {
                    addFlag = true;
                    MetadataAttributeVO metadataAttributeVO = createMetadataAttributeVO(MetadataConstants.COLUMN,
                            datasourceColumnVO.getColumnType(),
                            metadataColumnDTO.getColumnType(),
                            MetadataConstants.COLUMN_TYPE);
                    metadataAttributeVOS.add(metadataAttributeVO);
                }
                //字段长度
                if (notEqualsTwoString(metadataColumnDTO.getColumnLength(), datasourceColumnVO.getColumnLength())) {
                    addFlag = true;
                    MetadataAttributeVO metadataAttributeVO = createMetadataAttributeVO(MetadataConstants.COLUMN,
                            datasourceColumnVO.getColumnLength(),
                            metadataColumnDTO.getColumnLength(),
                            MetadataConstants.COLUMN_LENGTH);
                    metadataAttributeVOS.add(metadataAttributeVO);
                }
                //字段精度
                if (notEqualsTwoString(metadataColumnDTO.getColumnScale(), datasourceColumnVO.getColumnScale())) {
                    addFlag = true;
                    MetadataAttributeVO metadataAttributeVO = createMetadataAttributeVO(MetadataConstants.COLUMN,
                            datasourceColumnVO.getColumnScale(),
                            metadataColumnDTO.getColumnScale(),
                            MetadataConstants.COLUMN_SCALE);
                    metadataAttributeVOS.add(metadataAttributeVO);

                }
                //字段注释
                if (notEqualsTwoString(metadataColumnDTO.getColumnComment(), datasourceColumnVO.getColumnComment())) {
                    addFlag = true;
                    MetadataAttributeVO metadataAttributeVO = createMetadataAttributeVO(MetadataConstants.COLUMN,
                            datasourceColumnVO.getColumnComment(),
                            metadataColumnDTO.getColumnComment(),
                            MetadataConstants.COLUMN_COMMENT);
                    metadataAttributeVOS.add(metadataAttributeVO);
                }
                //字段主键
                if (notEqualsTwoString(metadataColumnDTO.getColumnPrimaryKey(), datasourceColumnVO.getColumnPrimaryKey())) {
                    addFlag = true;
                    MetadataAttributeVO metadataAttributeVO = createMetadataAttributeVO(MetadataConstants.COLUMN,
                            datasourceColumnVO.getColumnPrimaryKey(),
                            metadataColumnDTO.getColumnPrimaryKey(),
                            MetadataConstants.COLUMN_PRIMARY_KEY);
                    metadataAttributeVOS.add(metadataAttributeVO);
                }
                //字段外键
                if (notEqualsTwoString(metadataColumnDTO.getColumnForeignKey(), datasourceColumnVO.getColumnForeignKey())) {
                    addFlag = true;
                    MetadataAttributeVO metadataAttributeVO = createMetadataAttributeVO(MetadataConstants.COLUMN,
                            datasourceColumnVO.getColumnForeignKey(),
                            metadataColumnDTO.getColumnForeignKey(),
                            MetadataConstants.COLUMN_FOREIGN_KEY);
                    metadataAttributeVOS.add(metadataAttributeVO);
                }
                //唯一约束
                if (notEqualsTwoString(metadataColumnDTO.getColumnUnique(), datasourceColumnVO.getColumnUnique())) {
                    addFlag = true;
                    MetadataAttributeVO metadataAttributeVO = createMetadataAttributeVO(MetadataConstants.COLUMN,
                            datasourceColumnVO.getColumnUnique(),
                            metadataColumnDTO.getColumnUnique(),
                            MetadataConstants.COLUMN_UNIQUE);
                    metadataAttributeVOS.add(metadataAttributeVO);
                }
            }
            if (addFlag) {
                MetadataAttributeColVO metadataAttributeColVO = new MetadataAttributeColVO();
                metadataAttributeColVO.setType(MetadataConstants.COLUMN);
                metadataAttributeColVO.setSourceMetadataColumnVO(datasourceColumnVO);
                metadataAttributeColVO.setTargetMetadataColumnVO(metadataColumnDTO);
                metadataAttributeColVO.setUpdateColMetadata(metadataAttributeVOS);
                updateColMetadata.add(metadataAttributeColVO);
            }
        }

        metadataChangeReturnVO.setInsertMetadataColumnVO(needToInsert);
        metadataChangeReturnVO.setDeleteMetadataColumnVO(needToDelete);
        if (CollectionUtils.isNotEmpty(updateColMetadata)) {
            metadataChangeReturnVO.setOpt(MetadataConstants.UPDATE);
            metadataChangeReturnVO.setUpdateColMetadata(updateColMetadata);
        } else {
            String msg = String.format(MetadataConstants.METADATA_TABLE_OR_VIEW_NOUPDATE, datasourceDTO.getDataName(), tempTableName.getTableType(), tempTableName.getTableName());
            metadataChangeReturnVO.setMsg(msg);
        }
        return metadataChangeReturnVO;
    }

    /**
     * 构建属性变更对象
     */
    public static MetadataAttributeVO createMetadataAttributeVO(String type,
                                                                Object source,
                                                                Object target,
                                                                String attributeType) throws Exception {
        MetadataAttributeVO metadataAttributeVO = new MetadataAttributeVO();
        metadataAttributeVO.setType(type);
        metadataAttributeVO.setSourceMeta(source);
        metadataAttributeVO.setTargetMeta(target);
        metadataAttributeVO.setColumnChangeAttribute(attributeType);
        return metadataAttributeVO;

    }

    /**
     * 比较两个字符串是否相等，会将null和空字符串认为是同一种值
     *
     * @return 不相等返回true，相等返回false
     */
    private static boolean notEqualsTwoString(String str1, String str2) {
        str1 = str1 == null ? "" : str1;
        str2 = str2 == null ? "" : str2;
        return !str1.equals(str2);
    }

    /**
     * 获取表字段元数据
     */
    public static List<MetadataChangeColumn> getColumnsByTable(DatasourceDTO datasourceDTO, String tableId, LocalDateTime dateNow, String tableName, String dataSourceId) throws Exception {
        List<MetadataChangeColumn> columnProInsert = new ArrayList<>();
        //获取字段信息
        List<JSONObject> columns = DatasourceUtils.tableColumn(datasourceDTO, false);
        for (JSONObject column : columns) {
            MetadataChangeColumn columnDTO = new MetadataChangeColumn();

            //字段uuid生成
            String uuid = uuidToMD5(datasourceDTO.getDbName(), tableName, column.getString("key"), dataSourceId);
            columnDTO.setMetadataColumnUuid(uuid);
            columnDTO.setMetadataTableUuid(tableId);
            columnDTO.setColumnName(column.getString("key"));
            String type = column.getString("type");
            columnDTO.setColumnType(type);
            Integer dataType = column.getInteger("dataType");
            columnDTO.setDataType(dataType == null ? "" : String.valueOf(dataType));
            String comment = column.getString("comment");
            columnDTO.setColumnComment(comment);
            columnDTO.setColumnAlias(comment);
            Integer precision = column.getInteger("precision");
            columnDTO.setColumnLength(precision == null ? "" : String.valueOf(precision));
            //获取数据精度
            Integer scale = column.getInteger("scale");
            columnDTO.setColumnScale(String.valueOf(scale));
            Integer columnSort = column.getInteger("columnSort");
            columnDTO.setColumnSort(Long.valueOf(columnSort));
            //是否是主键
            Boolean pkFlag = column.getBoolean("pkflag");
            if (pkFlag) {
                columnDTO.setColumnPrimaryKey("1");
            } else {
                columnDTO.setColumnPrimaryKey("0");
            }
            //是否是外键
            Boolean fkFlag = column.getBoolean("fkflag");
            if (fkFlag) {
                columnDTO.setColumnForeignKey("1");
            } else {
                columnDTO.setColumnForeignKey("0");
            }
            //是否有唯一约束
            Boolean uniqueFlag = column.getBoolean("uniqueFlag");
            if (uniqueFlag) {
                columnDTO.setColumnUnique("1");
            } else {
                columnDTO.setColumnUnique("0");
            }
            //是否为空
            Boolean notNullFlag = column.getBoolean("notNullFlag");
            if (notNullFlag) {
                columnDTO.setColumnNull("1");
            } else {
                columnDTO.setColumnNull("0");
            }
            //获取默认值
            String defaultValue = column.getString("defaultValue");
            columnDTO.setColumnDefaultValue(defaultValue);
            //获取数据类型
            String dateType = column.getString("dateType");
            columnDTO.setColumnDataType(dateType);
            columnProInsert.add(columnDTO);
        }
        return columnProInsert;
    }

    /**
     * 获得字段的md5值
     *
     * @param dbName       库名
     * @param tableName    表名
     * @param columnName   字段名
     * @param datasourceId 数据源uuid
     * @return 文件的md5
     */
    public static String uuidToMD5(String dbName, String tableName, String columnName, String datasourceId) throws Exception {
        byte[] secretBytes = null;
        try {
            secretBytes =
                    MessageDigest.getInstance("md5").digest((dbName + "_" + tableName + "_" + columnName + "_" + datasourceId).getBytes());
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("没有这个md5算法！");
        }
        String md5code = new BigInteger(1, secretBytes).toString(16);
        for (int i = 0; i < 32 - md5code.length(); i++) {
            md5code = "0" + md5code;
        }
        return md5code;
    }

    /**
     * 获取当前数据源的版本
     *
     * @param datasourceDTO 数据源工具参数
     * @return 数据源版本
     */
    public static Boolean isTableExistsInDatabase(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        ISourceDTO iSourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(), datasourceDTO.getDataTypeCode(), datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        IClient iClient = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder()
                .tableName(datasourceDTO.getTableName())
                .schema(datasourceDTO.getSchema())
                .dbName(datasourceDTO.getDbName())
                .view(datasourceDTO.isView())
                .build();
        Boolean tableExistsInDatabase = iClient.isTableExistsInDatabase(iSourceDTO,sqlQueryDTO);
        return tableExistsInDatabase;
    }


    /**
     * 将数据源信息存放到map
     */
    public static DatasourceDTO datasourceDTOMapPut(String datasourceInfoId, DatasourceDTO datasourceDTO) {
        if (datasourceDTOMap.containsKey(datasourceInfoId)) {
            datasourceDTO = datasourceDTOMap.get(datasourceInfoId);
        } else {
            datasourceDTOMap.put(datasourceInfoId, datasourceDTO);
        }
        return datasourceDTO;
    }

    /**
     * SQL语法检查
     *
     * @param sql
     * @param DbTypeName
     * @return
     */
    public static boolean parseSql(String sql, String DbTypeName) {
        boolean isTrue = false;
        try {
            DbType DbType = com.alibaba.druid.DbType.of(DbTypeName);
            //先格式化sql
            String formatSql = SQLUtils.format(sql, DbType);
            //再校验sql语法
            SQLStatement sqlStatement = SQLUtils.parseSingleStatement(formatSql, DbType, false);
            if (Objects.nonNull(sqlStatement)) {
                isTrue = true;
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return isTrue;
    }

    /**
     * 返回数据库的字符集
     *
     * @return
     * @throws Exception
     */
    public static String getCharacterSetByDatabase(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        Integer dataTypeCode = datasourceDTO.getDataTypeCode();
        IClient client = ClientCache.getClient(dataTypeCode);
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(), dataTypeCode,
                datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder().tableName(datasourceDTO.getTableName()).build();
        try {
            String characterSetByDatabase = client.getCharacterSetByDatabase(sourceDTO, sqlQueryDTO);
            return characterSetByDatabase;
        } catch (Exception e) {
            return "";
        }

    }

    /**
     * 获取时区
     *
     * @return
     * @throws Exception
     */
    public static String getTimeZoneByDatabase(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        Integer dataTypeCode = datasourceDTO.getDataTypeCode();
        IClient client = ClientCache.getClient(dataTypeCode);
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(), dataTypeCode,
                datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder().tableName(datasourceDTO.getTableName()).build();
        try {
            String timeZoneByDatabase = client.getTimeZoneByDatabase(sourceDTO, sqlQueryDTO);
            return timeZoneByDatabase;
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 获取时区
     *
     * @return
     * @throws Exception
     */
    public static DatasourceInfoDTO getDataSourceImport(@NotNull DatasourceDTO datasourceDTO,
                                                        String businessUuid,
                                                        DatasourceInfoImportVO datasourceInfoImportVO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        Integer dataTypeCode = datasourceDTO.getDataTypeCode();
        IClient client = ClientCache.getClient(dataTypeCode);
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(datasourceDTO.getDataJsonMap(), dataTypeCode,
                datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder().
                businessUuid(businessUuid)
                .datasourceInfoImportVO(datasourceInfoImportVO)
                .build();
        try {
            DatasourceInfoDTO dataSourceImport = client.getDataSourceImport(sourceDTO, sqlQueryDTO);
            return dataSourceImport;
        } catch (Exception e) {
            return new DatasourceInfoDTO();
        }
    }

    /**
     * 获取存储过程
     *
     */
    public static ProcedureMetadata getProduce(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        ProcedureMetadata metadata=new ProcedureMetadata();
        try{
            JSONObject dataJsonMap = datasourceDTO.getDataJsonMap();
            IClient client = ClientCache.getClient(datasourceDTO.getDataTypeCode());
            ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(dataJsonMap, datasourceDTO.getDataTypeCode(),
                    datasourceDTO.getKerberosConfig(), Maps.newHashMap());
            SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder()
                    .schema(datasourceDTO.getSchema())
                    .objectName(datasourceDTO.getObjectName())
                    .datasourceId(datasourceDTO.getDatasourceInfoIdInKerberosPath())
                    .build();
            metadata=client.getProduce(sourceDTO, sqlQueryDTO);
        }catch (Exception e){
            throw new RdosDefineException("获取存储过程失败："+e.getMessage());
        }
        return metadata;
    }


    /**
     * 获取存储过程
     *
     */
    public static  List<ProcedureMetadataArguments> getProduceArguments(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        List<ProcedureMetadataArguments> list=new ArrayList<>();
        try{
            JSONObject dataJsonMap = datasourceDTO.getDataJsonMap();
            IClient client = ClientCache.getClient(datasourceDTO.getDataTypeCode());
            ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(dataJsonMap, datasourceDTO.getDataTypeCode(),
                    datasourceDTO.getKerberosConfig(), Maps.newHashMap());
            SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder()
                    .schema(datasourceDTO.getSchema())
                    .objectName(datasourceDTO.getObjectName())
                    .type(datasourceDTO.getType())
                    .build();
            list=client.getProduceArguments(sourceDTO, sqlQueryDTO);
        }catch (Exception e){
            throw new RdosDefineException("获取存储过程参数失败："+e.getMessage());
        }
        return list;
    }

    /**
     * 获取表标签
     *
     * @param datasourceDTO 数据源工具参数
     * @return 表注释
     */
    public static String getTableLabel(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        JSONObject dataJson = datasourceDTO.getDataJsonMap();
        ISourceDTO iSourceDTO = SourceDTOType.getSourceDTO(dataJson, datasourceDTO.getDataTypeCode(), datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder()
                .tableName(datasourceDTO.getTableName())
                .schema(datasourceDTO.getSchema())
                .build();
        IClient iClient = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        return iClient.getTableLabel(iSourceDTO, sqlQueryDTO);
    }

    /**
     * 获取DDL
     *
     * @param datasourceDTO 数据源工具参数
     * @return 所有schema
     */
    public static String getDDL(@NotNull DatasourceDTO datasourceDTO) {
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        JSONObject dataJson = datasourceDTO.getDataJsonMap();
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(dataJson, datasourceDTO.getDataTypeCode(), datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder()
                .tableName(datasourceDTO.getTableName())
                .schema(dataJson.getString("schema"))
                .view(datasourceDTO.isView())
                .build();
        IClient client = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        return client.getCreateTableSql(sourceDTO, sqlQueryDTO);
    }

    public static String getOracleNLS(@NotNull DatasourceDTO datasourceDTO) {
        String result = "";
        //设置插件包路径
        if (datasourceDTO.isInitDataSourcePluginPath()) {
            initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
        }
        String nlsLangSQL = "WITH nls_lang AS( SELECT '1' AS NUM,VALUE||'_' AS VALUE FROM NLS_DATABASE_PARAMETERS WHERE PARAMETER IN ('NLS_LANGUAGE') UNION ALL SELECT '2' AS NUM,VALUE||'.' AS VALUE FROM NLS_DATABASE_PARAMETERS WHERE PARAMETER IN ('NLS_TERRITORY') UNION ALL SELECT '3' AS NUM,VALUE AS VALUE FROM NLS_DATABASE_PARAMETERS WHERE PARAMETER IN ('NLS_CHARACTERSET') ) SELECT LISTAGG(VALUE,'') WITHIN GROUP(ORDER BY NUM) AS VALUE FROM nls_lang";
        JSONObject dataJson = datasourceDTO.getDataJsonMap();
        ISourceDTO sourceDTO = SourceDTOType.getSourceDTO(dataJson, datasourceDTO.getDataTypeCode(), datasourceDTO.getKerberosConfig(), Maps.newHashMap());
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder()
                .sql(nlsLangSQL)
                .build();
        IClient client = ClientCache.getClient(datasourceDTO.getDataTypeCode());
        try {
            List<Map<String, Object>> list = client.executeQuery(sourceDTO, sqlQueryDTO);
            if (CollectionUtils.isNotEmpty(list)) {
                for (Map<String, Object> stringObjectMap : list) {
                    result = stringObjectMap.get("VALUE").toString();
                }
            }
        } catch (Exception e) {
            log.error("get nls error", e);
        }
        return result;
    }
}
