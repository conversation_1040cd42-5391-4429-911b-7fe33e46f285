package com.dsg.database.datasource.enums;

import com.dsg.database.datasource.exception.ErrorCode;
import com.dsg.database.datasource.exception.PubSvcDefineException;
import com.dtstack.dtcenter.loader.source.DataBaseType;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 数据源类型枚举类 对应{@link com.dtstack.dtcenter.loader.source.DataSourceType}
 *
 * <AUTHOR>
 * @date 2022/7/12
 */
public enum DataSourceTypeEnum {

    /**
     * RDBMS
     */
    MySQL(1, "MySQL", "5.x", DataBaseType.MySql.getDriverClassName(), "`", "`", "`", "`"),
    StarRocks(72, "StarRocks", "FOR MySQL5.x", DataBaseType.MySql.getDriverClassName(), "`", "`", "`", "`"),
    GoldenDB(73, "GoldenDB", "5.x", DataBaseType.GoldenDB.getDriverClassName(), "`", "`", "`", "`"),
    MySQL8(1001, "MySQL", "8.x", DataBaseType.MySql8.getDriverClassName(), "`", "`", "`", "`"),
    MySQL8_HIVE(2024, "MySQL8_HIVE", "", DataBaseType.MySql8.getDriverClassName(), "`", "`", "`", "`"),
    MySQL5_HIVE(2025, "MySQL5_HIVE", "", DataBaseType.MySql.getDriverClassName(), "`", "`", "`", "`"),
    MySQLPXC(98, "MySQL PXC", null, "", "`", "`", "`", "`"),
    Polardb_For_MySQL(28, "PolarDB for MySQL8", null, DataBaseType.Polardb_For_MySQL.getDriverClassName(), "`", "`", "`", "`"),
    Oracle(2, "Oracle", "11.x", DataBaseType.Oracle.getDriverClassName(), "\"", "\"", "\"", "\""),
    Oracle_9i(2014, "Oracle", "9i", DataBaseType.Oracle.getDriverClassName(), "\"", "\"", "\"", "\""),
    Oracle_19c(2015, "Oracle", "19c", DataBaseType.Oracle.getDriverClassName(), "\"", "\"", "\"", "\""),
    SQLServer(3, "SQLServer", "2017_earlier", DataBaseType.SQLServer.getDriverClassName(), "[", "]", "[", "]"),
    SQLSERVER_2017_LATER(32, "SQLServer", "2017_later", DataBaseType.SQLSSERVER_2017_LATER.getDriverClassName(), "[", "]", "[", "]"),
    PostgreSQL(4, "PostgreSQL", "9.4", DataBaseType.PostgreSQL.getDriverClassName(), "\"", "\"", "\"", "\""),
    TBase(2018, "TBase", "42.x", DataBaseType.TBase.getDriverClassName(), "\"", "\"", "\"", "\""),
    DWS_PG(2008, "DWS_PG", "9.4", DataBaseType.DWS_PG.getDriverClassName(), "\"", "\"", "\"", "\""),
    DWS_MySQL(2009, "DWS_MySQL", "5.x", DataBaseType.DWS_MySQL.getDriverClassName(), "`", "`", "`", "`"),
    ADB_PostgreSQL(54, "AnalyticDB_PostgreSQL", "9.4", DataBaseType.ADB_FOR_PG.getDriverClassName(), "\"", "\"", "\"", "\""),
    DB2(19, "DB2", "11.5.8.0", DataBaseType.DB2.getDriverClassName(), "\"", "\"", "\"", "\""),
    DB2_AS400(1009, "DB2_AS400", "5.0", DataBaseType.DB2_AS400.getDriverClassName(), "\"", "\"", "\"", "\""),
    DMDB(35, "DMDB", "8.1.1.134", DataBaseType.DMDB.getDriverClassName(), "\"", "\"", "\"", "\""),
    //DMDB_FOR_MySQL(35, "DMDB", "For MySQL", DataBaseType.DMDB.getDriverClassName(), "`", "`", "`", "`"),
    RDBMS(5, "RDBMS", null, DataBaseType.RDBMS.getDriverClassName(), "\"", "\"", "\"", "\""),
    KINGBASE8(40, "KINGBASE8", "V8R6", DataBaseType.KINGBASE8.getDriverClassName(), "\"", "\"", "\"", "\""),
    Sequoiadb_FOR_MYSQL(1, "Sequoiadb", "FOR MySQL5.x", DataBaseType.Sequoiadb_FOR_MYSQL.getDriverClassName(), "`", "`", "`", "`"),

    HIVE1X(27, "Hive", "1.x", DataBaseType.HIVE1X.getDriverClassName(), "`", "`", "`", "`"),
    HIVE2X(7, "Hive", "2.x", DataBaseType.HIVE.getDriverClassName(), "`", "`", "`", "`"),
    HIVE3X(50, "Hive", "3.x", DataBaseType.HIVE3.getDriverClassName(), "`", "`", "`", "`"),
    HIVE3X_MRS(74, "Hive", "3.x.mrs", DataBaseType.HIVE3_MRS.getDriverClassName(), "`", "`", "`", "`"),
    SparkThrift2_1(45, "SparkThrift", null, "", "", "", "", ""),
    MAXCOMPUTE(10, "Maxcompute", "ODPS", DataBaseType.MaxCompute.getDriverClassName(), "`", "`", "`", "`"),
    GREENPLUM6(36, "Greenplum", "6.x", DataBaseType.Greenplum6.getDriverClassName(), "\"", "\"", "\"", "\""),
    GREENPLUM_PostgreSQL(66, "Greenplum_PostgreSQL", "9.4", DataBaseType.GP_FOR_PG.getDriverClassName(), "\"", "\"", "\"", "\""),
    LIBRA(21, "GaussDB", null, DataBaseType.LIBRA.getDriverClassName(), "\"", "\"", "\"", "\""),
    GBase_8a(22, "GBase", "8a", DataBaseType.GBase8a.getDriverClassName(), "`", "`", "`", "`"),
    HDFS(6, "HDFS", "2.x", "", "", "", "", ""),
    HDFS3(69, "HDFS3", "3.x", "", "", "", "", ""),
    HDFS_MRS(2021, "HDFS", "MRS", "", "", "", "", ""),
    HDFS_TBDS(60, "HDFS", "TBDS", "", "", "", "", ""),
    FTP(9, "FTP", "0.1.55", "", "", "", "", ""),
    SFTP(2005, "SFTP", "0.1.55", "", "", "", "", ""),
    HOST(1008, "HOST", "0.1.55", "", "", "", "", ""),
    IMPALA(29, "Impala", "2.6.x", DataBaseType.Impala.getDriverClassName(), "`", "`", "`", "`"),
    ClickHouse(25, "ClickHouse", "21.3", DataBaseType.Clickhouse.getDriverClassName(), "`", "`", "`", "`"),
    ClickHouse_MRS(2023, "ClickHouse", "MRS", DataBaseType.Clickhouse_Mrs.getDriverClassName(), "`", "`", "`", "`"),
    TiDB(31, "TiDB", "FOR MySQL5.x", DataBaseType.TiDB.getDriverClassName(), "`", "`", "`", "`"),
    CarbonData(20, "CarbonData", null, DataBaseType.CarbonData.getDriverClassName(), "\"", "\"", "\"", "\""),
    Kudu(24, "Kudu", "1.10.1", DataBaseType.Kudu.getDriverClassName(), "`", "`", "`", "`"),
    Kylin(58, "Kylin URL", "3.x", DataBaseType.Kylin.getDriverClassName(), "`", "`", "`", "`"),
    HBASE(8, "HBase", "1.x", "", "", "", "", ""),
    HBASE2(39, "HBase", "2.x", "", "", "", "", ""),
    HBASE_TBDS(61, "HBase", "TBDS", "", "", "", "", ""),
    HBASE_MRS(2022, "HBase", "MRS", "", "", "", "", ""),
    Phoenix4(30, "Phoenix", "4.x", DataBaseType.Phoenix.getDriverClassName(), "`", "`", "`", "`"),
    Phoenix5(38, "Phoenix", "5.x", DataBaseType.Phoenix5.getDriverClassName(), "`", "`", "`", "`"),
    ES(11, "Elasticsearch", "5.x", "", "", "", "", ""),
    ES6(33, "Elasticsearch", "6.x", "", "", "", "", ""),
    ES7(46, "Elasticsearch", "7.x", "", "", "", "", ""),
    ES8(2033, "Elasticsearch", "8.x", "", "", "", "", ""),
    MONGODB(13, "MongoDB", "3.12.4", "", "", "", "", ""),
    SAP_HANA(76, "SAP_HANA", "2.4.51", DataBaseType.sapHana1.getDriverClassName(), "\"", "\"", "\"", "\""),
    REDIS(12, "Redis", "3.0_later", "", "", "", "", ""),
    S3(41, "S3", null, "", "", "", "", ""),
    KAFKA_TBDS(62, "Kafka", "TBDS", "", "", "", "", ""),
    KAFKA(26, "Kafka", "1.x", "", "", "", "", ""),
    KAFKA_2X(37, "Kafka", "2.x", "", "", "", "", ""),
    KAFKA_09(18, "Kafka", "0.9", "", "", "", "", ""),
    KAFKA_10(17, "Kafka", "0.10", "", "", "", "", ""),
    KAFKA_11(14, "Kafka", "0.11", "", "", "", "", ""),
    HIGH_VERSION_KAFKA(68, "Kafka", "2.0", "", "", "", "", ""),
    EMQ(34, "EMQ", null, "", "", "", "", ""),
    WEB_SOCKET(42, "WebSocket", null, "", "", "", "", ""),
    VERTICA(43, "Vertica", null, "", "", "", "", ""),
    SOCKET(44, "Socket", null, "", "", "", "", ""),
    ADS(15, "AnalyticDB_MySQL", "5.x", DataBaseType.ADS.getDriverClassName(), "`", "`", "`", "`"),
    Presto(48, "Presto", null, DataBaseType.Presto.getDriverClassName(), "`", "`", "`", "`"),
    SOLR(53, "Solr", "7.x", "", "", "", "", ""),
    INFLUXDB(55, "InfluxDB", "1.x", "", "", "", "", ""),
    INCEPTOR(52, "Inceptor", "6.0.2", DataBaseType.INCEPTOR.getDriverClassName(), "`", "`", "`", "`"),
    INCEPTOR8(2012, "Inceptor", "8.x", DataBaseType.INCEPTOR8.getDriverClassName(), "`", "`", "`", "`"),
    AWS_S3(51, "AWS_S3", "1.11.636", "", "", "", "", ""),
    OSS_ALI(2006, "ALI_OSS", "3.13.1", "", "", "", "", ""),
    DATA_HUB(2013, "DataHub", "2.x", "", "", "", "", ""),
    OSS_HUAWEI(2011, "HUAWEI_OBS", "3.21.4", "", "", "", "", ""),
    OSS_LC(2010, "INSPUR_OSS", "4.0.19", "", "", "", "", ""),
    OPENTSDB(56, "OpenTSDB", "2.x", "", "", "", "", ""),
    Doris_JDBC(57, "Doris", "0.x", DataBaseType.Doris.getDriverClassName(), "`", "`", "`", "`"),
    Kylin_Jdbc(23, "Kylin JDBC", "3.x", "", "", "", "", ""),
    OceanBase_FOR_MySQL(49, "OceanBase_FOR_MySQL", "4.x", DataBaseType.OceanBase.getDriverClassName(), "`", "`", "`", "`"),
    OceanBase_FOR_ORACLE(1006, "OceanBase_FOR_ORACLE", "4.x", DataBaseType.OceanBase.getDriverClassName(), "\"", "\"", "\"", "\""),
    RESTFUL(47, "Restful", null, "", "", "", "", ""),
    TRINO(59, "Trino", null, DataBaseType.TRINO.getDriverClassName(), "", "", "", ""),
    DORIS_HTTP(64, "Doris", "HTTP", "", "", "", "", ""),
    DORIS1(2016, "Doris", "1.x", DataBaseType.Doris.getDriverClassName(), "`", "`", "`", "`"),
    DORIS2(2017, "Doris", "2.x",  DataBaseType.Doris.getDriverClassName(), "`", "`", "`", "`"),
    DMDB_FOR_ORACLE(67, "DMDB", "For Oracle", DataBaseType.DMDB_For_Oracle.getDriverClassName(), "\"", "\"", "\"", "\""),
    MariaDB(63, "MariaDB", "11.2.1", DataBaseType.MariaDB.getDriverClassName(), "`", "`", "`", "`"),
    TDSQL_FOR_MySQL(1007, "TDSQL_FOR_MySQL", "8.x", DataBaseType.MySql.getDriverClassName(), "`", "`", "`", "`"),
    TDSQL_FOR_PG(2019, "TDSQL_FOR_PG", "42.x", DataBaseType.TDSQL_FOR_PG.getDriverClassName(), "\"", "\"", "\"", "\""),
    TDSQL_FOR_ORACLE(2020, "TDSQL_FOR_ORACLE", "42.x", DataBaseType.TDSQL_FOR_ORACLE.getDriverClassName(), "\"", "\"", "\"", "\""),
    GaussDB(2000, "GaussDB", "5.0.0", DataBaseType.GaussDB.getDriverClassName(), "\"", "\"", "\"", "\""),
    GaussDB_HIVE(2026, "GaussDB_HIVE", "", DataBaseType.GaussDB.getDriverClassName(), "\"", "\"", "\"", "\""),
    Gauss_DB200(2007, "Gauss_DB200", "2.0", DataBaseType.Gauss_DB200.getDriverClassName(), "\"", "\"", "\"", "\""),
    GaussDB_FOR_MySQL(2003, "GaussDB_FOR_MySQL", "5.x", DataBaseType.GaussDB_FOR_MySQL.getDriverClassName(), "`", "`", "`", "`"),
    DDM_FOR_MYSQL(2001, "ddm_for_mysql", null, DataBaseType.DDM_FOR_MYSQL.getDriverClassName(), "`", "`", "`", "`"),
    ArgoDB(2002, "ArgoDB", "6.0.0", DataBaseType.ArgoDB.getDriverClassName(), "`", "`", "`", "`"),
    Informix(1003, "Informix", "10.x~14.x", DataBaseType.Informix.getDriverClassName(), "\"", "\"", "\"", "\""),
    KunDB(1004, "KunDB", "3.0", DataBaseType.Informix.getDriverClassName(), "`", "`", "`", "`"),
    Sybase(2030, "Sybase", "JTDS-1.X", DataBaseType.Sybase_jTDS.getDriverClassName(), "\"", "\"", "\"", "\""),
    Sybase_Jconn(2031, "Sybase", "Jconn4", DataBaseType.Sybase_jConnect.getDriverClassName(), "\"", "\"", "\"", "\""),
    INSPUR_S3(2032, "INSPUR_S3", "1.X", "", "", "", "", ""),
    GaussDB_503(2034, "GaussDB", "503", DataBaseType.GaussDB.getDriverClassName(), "\"", "\"", "\"", "\""),
    ;

    private static final Table<String, String, DataSourceTypeEnum> CACHE = HashBasedTable.create();
    private static final Table<String, String, DataSourceTypeEnum> CACHE_LOWER = HashBasedTable.create();
    private static final Map<String, String> CACHE_LOWER_MAP = new HashMap<>();
    private static final Map<String, List<String>> CACHE_LOWER_VERSION = new HashMap<>();


    static {
        for (DataSourceTypeEnum value : DataSourceTypeEnum.values()) {
            CACHE.put(value.getDataType(), Objects.isNull(value.getDataVersion()) ? StringUtils.EMPTY : value.getDataVersion(), value);
            CACHE_LOWER.put(value.getDataType().toLowerCase(), Objects.isNull(value.getDataVersion()) ? StringUtils.EMPTY : value.getDataVersion(), value);
            CACHE_LOWER_MAP.put(value.getDataType().toLowerCase(),value.getDataType());
            List<String> list=new ArrayList<>();
            if(CACHE_LOWER_VERSION.containsKey(value.getDataType().toLowerCase())){
                List<String> strings = CACHE_LOWER_VERSION.get(value.getDataType().toLowerCase());
                strings.add(value.getDataVersion());
                CACHE_LOWER_VERSION.put(value.getDataType().toLowerCase(),strings);
            }else{
                list.add(value.getDataVersion());
                CACHE_LOWER_VERSION.put(value.getDataType().toLowerCase(),list);
            }
        }
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(DataSourceTypeEnum.class);

    DataSourceTypeEnum(Integer val, String dataType, String dataVersion, String driverClassName,
                       String tablePrefix, String tableSuffix, String columnPrefix, String columnSuffix) {
        this.val = val;
        this.dataType = dataType;
        this.dataVersion = dataVersion;
        this.driverClassName = driverClassName;
        this.tablePrefix = tablePrefix;
        this.tableSuffix = tableSuffix;
        this.columnPrefix = columnPrefix;
        this.columnSuffix = columnSuffix;
    }

    /**
     * 根据数据源类型和版本获取数据源枚举信息
     *
     * @param dataType
     * @param dataVersion
     * @return
     */
    public static DataSourceTypeEnum typeVersionOf(String dataType, String dataVersion) {
        if (StringUtils.isBlank(dataVersion)) {
            dataVersion = StringUtils.EMPTY;
        }
        DataSourceTypeEnum value;
        if ((value = CACHE.get(dataType, dataVersion)) == null) {
            LOGGER.error("this dataType cannot find,dataType:{},dataVersion:{}", dataType, dataVersion);
            throw new PubSvcDefineException(ErrorCode.CAN_NOT_FITABLE_SOURCE_TYPE);
        }
        return value;
    }
    /**
     * 根据数据源类型和版本获取数据源枚举信息--类型小写
     *
     * @param dataType
     * @param dataVersion
     * @return
     */
    public static DataSourceTypeEnum lowerTypeVersionOf(String dataType, String dataVersion) {
        if (StringUtils.isBlank(dataVersion)) {
            dataVersion = StringUtils.EMPTY;
        }
        DataSourceTypeEnum value;
        if ((value = CACHE_LOWER.get(dataType, dataVersion)) == null) {
            LOGGER.error("this dataType cannot find,dataType:{},dataVersion:{}", dataType, dataVersion);
            throw new PubSvcDefineException(ErrorCode.CAN_NOT_FITABLE_SOURCE_TYPE);
        }
        return value;
    }

    /**
     * 根据数据源类型和版本获取数据源枚举信息--类型小写
     *
     * @param dataType
     * @return
     */
    public static List<String> versions(String dataType) {
        List<String> versions= Lists.newArrayList();
        if ((versions = CACHE_LOWER_VERSION.get(dataType)) == null){
            LOGGER.error("this dataType cannot find,dataType:{}", dataType);
        }
        return versions;
    }
    /**
     * 根据数据源类型和版本获取数据源枚举信息--类型小写
     *
     * @param dataType
     * @return
     */
    public static String lowerTypeVersionOf(String dataType) {
        String value="";
        if ((value = CACHE_LOWER_MAP.get(dataType)) == null){
            LOGGER.error("this dataType cannot find,dataType:{}", dataType);
        }
        return value;
    }



    /**
     * 根据数据源val获取数据源枚举信息
     *
     * @param val
     * @return
     */
    public static DataSourceTypeEnum valOf(Integer val) {
        Objects.requireNonNull(val);
        for (DataSourceTypeEnum value : DataSourceTypeEnum.values()) {
            if (Objects.equals(value.getVal(), val)) {
                return value;
            }
        }
        LOGGER.error("can not find this dataTypeCode:{}", val);
        throw new PubSvcDefineException(ErrorCode.CAN_NOT_FITABLE_SOURCE_TYPE);
    }

    /**
     * 数据源值
     */
    private Integer val;
    /**
     * 数据源类型
     */
    private String dataType;
    /**
     * 数据源版本(可为空)
     */
    private String dataVersion;
    /**
     * 驱动名（来源：com.dtstack.dtcenter.loader.source.DataBaseType）
     */
    private String driverClassName;

    /**
     * 库表的标志符
     */
    private String tablePrefix;
    private String tableSuffix;
    /**
     * 字段的标志符
     */
    private String columnPrefix;
    private String columnSuffix;

    public Integer getVal() {
        return val;
    }

    public void setVal(Integer val) {
        this.val = val;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getDataVersion() {
        return dataVersion;
    }

    public void setDataVersion(String dataVersion) {
        this.dataVersion = dataVersion;
    }

    public String getDriverClassName() {
        return driverClassName;
    }

    public void setDriverClassName(String driverClassName) {
        this.driverClassName = driverClassName;
    }

    public String getTablePrefix() {
        return tablePrefix;
    }

    public void setTablePrefix(String tablePrefix) {
        this.tablePrefix = tablePrefix;
    }

    public String getTableSuffix() {
        return tableSuffix;
    }

    public void setTableSuffix(String tableSuffix) {
        this.tableSuffix = tableSuffix;
    }

    public String getColumnPrefix() {
        return columnPrefix;
    }

    public void setColumnPrefix(String columnPrefix) {
        this.columnPrefix = columnPrefix;
    }

    public String getColumnSuffix() {
        return columnSuffix;
    }

    public void setColumnSuffix(String columnSuffix) {
        this.columnSuffix = columnSuffix;
    }

    /**
     * 根据数据库类型拼接特殊字符，如查询中文表名需要加双引号
     *
     * @param value 传入的表名或者库名
     * @return 拼接字符后的值
     */
    public String getTablePrefixAndSuffixValue(String value) {
        return String.format("%s%s%s", this.tablePrefix, value, this.tableSuffix);
    }

    /**
     * 根据数据库类型拼接特殊字符，如查询中文字段名需要加双引号
     *
     * @param value 传入的字段名
     * @return 拼接字符后的值
     */
    public String getColumnPrefixAndSuffixValue(String value) {
        return String.format("%s%s%s", this.columnPrefix, value, this.columnSuffix);
    }

    @Override
    public String toString() {
        if (Strings.isNotBlank(dataVersion)) {
            return dataType + "-" + dataVersion;
        }
        return dataType;
    }
}
