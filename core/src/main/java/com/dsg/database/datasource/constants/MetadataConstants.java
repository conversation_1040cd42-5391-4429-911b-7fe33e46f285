package com.dsg.database.datasource.constants;

/**
 * 元数据相关常量
 *
 * <AUTHOR>
 * @date 2023/2/13 12:07
 */
public interface MetadataConstants {
    //------------------ 发送消息用到的常量 ------------------
    /**
     * 元数据和字段删除
     */
    String METADATA_TABLE_OR_VIEW_DELETE = "数据源名称[%s] 表类型[%s] 表名称[%s]-源库中表不存在，删除元数据，同时删除当前表下面的字段元数据";

    /**
     * 字段类型发生改变
     */
    String COLUMN_TYPE = "columnType";

    /**
     * 字段长度发生改变
     */
    String COLUMN_LENGTH = "columnLength";

    /**
     * 字段精度发生改变
     */
    String COLUMN_SCALE = "columnScale";

    /**
     * 字段注释发生改变
     */
    String COLUMN_COMMENT = "columnComment";

    /**
     * 字段主键发生改变
     */
    String COLUMN_PRIMARY_KEY = "columnPrimaryKey";

    /**
     * 字段外键发生改变
     */
    String COLUMN_FOREIGN_KEY = "columnForeignKey";

    /**
     * 字段唯一约束发生改变
     */
    String COLUMN_UNIQUE = "columnUnique";

    //------------------ 元数据采集关键字常量 ------------------
    /**
     * 表
     */
    String TABLE = "table";
    /**
     * 视图
     */
    String VIEW = "view";

    //------------------ 元数据采集表、字段元数据历史记录用到的常量 ------------------
    /**
     * operationType：删除标记
     */
    String DELETE = "delete";
    /**
     * operationType：插入标记
     */
    String INSERT = "insert";

    /**
     * operationType：修改标记
     */
    String UPDATE = "update";
    /**
     * 表注释
     */
    String TABLE_COMMENT = "tableComment";
    /**
     * 表大小
     */
    String TABLE_LENGTH = "tableLength";
    /**
     * 表行数
     */
    String TABLE_ROWS = "tableRows";

    /**
     * 字段
     */
    String COLUMN = "column";
    /**
     * 更新元数据表
     */
    String METADATA_TABLE_OR_VIEW_NOUPDATE = "数据源名称[%s] 表类型[%s] 表名称[%s]-元数据没有发生改变";


}
