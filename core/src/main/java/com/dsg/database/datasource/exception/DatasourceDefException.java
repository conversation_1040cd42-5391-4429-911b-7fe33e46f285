package com.dsg.database.datasource.exception;

import com.dsg.database.datasource.utils.Strings;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/7/14
 */
public class DatasourceDefException extends RuntimeException {
    private int code;

    public DatasourceDefException(String message) {
        super(message);
        this.code = ErrorCode.UNKNOWN_ERROR.getCode();
    }

    public DatasourceDefException(String message, Throwable throwable) {
        super(message, throwable);
        this.code = ErrorCode.UNKNOWN_ERROR.getCode();
    }

    public DatasourceDefException(ExceptionEnums exEnum) {
        super(exEnum.getDescription());
        code = exEnum.getCode();
    }

    public DatasourceDefException(ExceptionEnums exEnum, String message) {

        super(StringUtils.isNotBlank(message) ? message : exEnum.getDescription());
        code = exEnum.getCode();
    }

    public DatasourceDefException(ExceptionEnums exEnum, String message, Object... args) {
        super(StringUtils.isNotBlank(message) ? Strings.format(message, args) : exEnum.getDescription());
        code = exEnum.getCode();
    }

    public DatasourceDefException(Throwable throwable, ExceptionEnums exEnum) {
        super(exEnum.getDescription(), throwable);
        code = exEnum.getCode();
    }

    public DatasourceDefException(Throwable throwable, ExceptionEnums exEnum, String message) {
        super(StringUtils.isNotBlank(message) ? message : exEnum.getDescription(), throwable);
        code = exEnum.getCode();
    }

    public DatasourceDefException(Throwable throwable, ExceptionEnums exEnum, String message, Object... args) {
        super(StringUtils.isNotBlank(message) ? Strings.format(message, args) : exEnum.getDescription(), throwable);
        code = exEnum.getCode();
    }

    public int getCode() {
        return code;
    }
}
