package com.dsg.database.datasource.utils;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: DsgDistCPParams
 * @Function: TODO
 * @Date: 2023/5/25 15:38
 */
@Data
@Builder
public class DsgDistCPParams {
    //备份模式
    private String cpModel;
    //源端路径
    private String sourcePath;
    //目标端路径
    private String targetPath;
    //是否追加
    private String isDistCpAppend;
    //源端HA活跃节点
    private String activeNNBySource;
    //目标端活跃节点
    private String activeNNByTarget;
    //带宽 默认是256M
    private String bandwidth;
    //map个数 默认是20
    private String maps;
    //yarn队列名
    private String yarnQueue;

    /**
     * kerberos 配置信息
     */
    private Map<String, Object> kerberosConfig;
}
