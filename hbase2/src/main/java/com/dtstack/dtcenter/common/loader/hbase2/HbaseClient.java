/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.hbase2;

import com.dtstack.dtcenter.common.loader.common.nosql.AbsNoSqlClient;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.TableViewDTO;
import com.dtstack.dtcenter.loader.dto.filter.RowFilter;
import com.dtstack.dtcenter.loader.dto.filter.TimestampFilter;
import com.dtstack.dtcenter.loader.dto.source.HbaseSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.enums.CompareOp;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.*;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.filter.Filter;
import org.apache.hadoop.hbase.filter.FilterList;
import org.apache.hadoop.hbase.filter.PageFilter;
import org.apache.hadoop.hbase.io.TimeRange;
import org.apache.hadoop.hbase.util.Bytes;

import java.io.Closeable;
import java.io.IOException;
import java.sql.Types;
import java.util.*;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 17:06 2020/7/9
 * @Description：Hbase 客户端
 */
@Slf4j
public class HbaseClient<T> extends AbsNoSqlClient<T> {
    private HbaseConnFactory hbaseConnFactory = new HbaseConnFactory();

    private static final String ROWKEY = "rowkey";

    private static final String FAMILY_QUALIFIER = "%s:%s";

    private static final String TIMESTAMP = "timestamp";

    @Override
    public Boolean testCon(ISourceDTO iSource) {
        return hbaseConnFactory.testConn(iSource);
    }

    @Override
    public List<String> getTableList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        List<String> tableList = new ArrayList<>();
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO, queryDTO);
            admin = hConn.getAdmin();
//            TableName[] tableNames = admin.listTableNames();
            TableDescriptor[] tableDescriptors = admin.listTableDescriptorsByNamespace(queryDTO.getSchema());
            if (tableDescriptors != null) {
                for (TableDescriptor tableDescriptor : tableDescriptors) {
                    TableName tableName = tableDescriptor.getTableName();
                    // 检查表是否已启用
                    if (admin.isTableEnabled(tableName)) {
                        tableList.add(tableName.getNameAsString());
                    }
                    tableList.add(tableName.getNameAsString());
                }
            }
        } catch (IOException e) {
            throw new DtLoaderException(String.format("get hbase table list exception：%s", e.getMessage()), e);
        } finally {
            closeAdmin(admin);
            closeConnection(hConn, hbaseSourceDTO);
        }
        return tableList;
    }


    @Override
    public List<TableViewDTO> getTableAndViewList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        List<TableViewDTO> tableList = new ArrayList<>();
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO, queryDTO);
            admin = hConn.getAdmin();
            String schema = queryDTO.getSchema();
            if(StringUtils.isEmpty(schema)){

            }
            TableDescriptor[] tableDescriptors = admin.listTableDescriptorsByNamespace(queryDTO.getSchema());
            List<String> disEnableList = new ArrayList<>();
            if (tableDescriptors != null) {
                for (TableDescriptor tableDescriptor : tableDescriptors) {
                    TableName tableName = tableDescriptor.getTableName();
                    // 检查表是否已启用
                    if (admin.isTableEnabled(tableName)) {
                        tableList.add(TableViewDTO.builder().name(tableName.getNameAsString()).type("TABLE").build());
                    }else{
                        disEnableList.add(tableName.getNameAsString());
                    }
                }
                String join = String.join(" 表、 ", disEnableList) + " 是禁用状态，此次不采集";
                log.warn(join);
            }
        } catch (IOException e) {
            throw new DtLoaderException(String.format("get hbase table list exception：%s", e.getMessage()), e);
        } finally {
            closeAdmin(admin);
            closeConnection(hConn, hbaseSourceDTO);
        }
        return tableList;
    }

    private static void closeConnection(Connection hConn, HbaseSourceDTO hbaseSourceDTO) {
        if ((hbaseSourceDTO.getPoolConfig() == null || MapUtils.isNotEmpty(hbaseSourceDTO.getKerberosConfig())) && hConn != null) {
            try {
                hConn.close();
            } catch (IOException e) {
                log.error("hbase Close connection exception", e);
            }
        }
    }

    private static void closeAdmin(Admin admin) {
        if (admin != null) {
            try {
                admin.close();
            } catch (IOException e) {
                log.error("hbase Close connection exception", e);
            }
        }
    }

    @Override
    public List<ColumnMetaDTO> getColumnMetaData(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Table tb = null;
        List<ColumnMetaDTO> cfList = new ArrayList<>();
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO, queryDTO);
            // 获取表描述符
            String tableNameStr = "";
            if (StringUtils.isNotEmpty(queryDTO.getSchema())) {
                tableNameStr = queryDTO.getSchema() + ":" + queryDTO.getTableName();
            } else {
                String tableName1 = queryDTO.getTableName();
                if(!tableName1.contains(":")){
                    tableNameStr = "default:" + tableName1;
                }else {
                    tableNameStr=queryDTO.getTableName();
                }
            }


            Admin admin = hConn.getAdmin();

            TableName tableName = TableName.valueOf(tableNameStr);
            // 获取表描述符
            TableDescriptor tableDescriptor = admin.getDescriptor(tableName);

            // 遍历列族信息
            ColumnFamilyDescriptor[] columnFamilies = tableDescriptor.getColumnFamilies();

            for (ColumnFamilyDescriptor columnFamily : columnFamilies) {
                // 打开表连接
                Table table = hConn.getTable(tableName);
                Scan scan = new Scan();
                scan.addFamily(columnFamily.getName());
                scan.setMaxResultSize(1);
                scan.setFilter(new PageFilter(1));
                // 扫描表以获取字段（列）
                ResultScanner scanner = table.getScanner(scan);
                for (Result result : scanner) {
                    result.getNoVersionMap().forEach((cf, columns) -> {
                        if (Bytes.toString(cf).equals(columnFamily.getNameAsString())) {
                            columns.forEach((column, value) -> {
                                System.out.println("Field: " + Bytes.toString(column));
                                String keyString = Bytes.toString(column);
                                ColumnMetaDTO columnMetaDTO = new ColumnMetaDTO();
                                columnMetaDTO.setKey(columnFamily.getNameAsString() + ":" + keyString);
                                columnMetaDTO.setType("String");
                                columnMetaDTO.setPrecision(Integer.MAX_VALUE);
                                columnMetaDTO.setScale(0);
                                columnMetaDTO.setDataType(Types.VARCHAR);
                                if (!cfList.contains(columnMetaDTO)) {
                                    cfList.add(columnMetaDTO);
                                }
                            });
                        }
                    });
                    break;
                }
                scanner.close();
            }

            //添加rowKey
            ColumnMetaDTO rowKey = new ColumnMetaDTO();
            rowKey.setKey(ROWKEY);
            rowKey.setType("String");
            rowKey.setPrecision(Integer.MAX_VALUE);
            rowKey.setScale(0);
            rowKey.setDataType(Types.VARCHAR);
            rowKey.setPkflag(true);
            cfList.add(rowKey);

            //添加timestanp
            ColumnMetaDTO time = new ColumnMetaDTO();
            time.setKey(TIMESTAMP);
            time.setType("long");
            time.setPrecision(Integer.MAX_VALUE);
            time.setScale(0);
            time.setDataType(Types.INTEGER);
            cfList.add(time);
        } catch (IOException e) {
            throw new DtLoaderException(String.format("hbase list column families error：%s", e.getMessage()), e);
        } finally {
            closeTable(tb);
            closeConnection(hConn, hbaseSourceDTO);
        }
        return cfList;
    }

    @Override
    public List<Map<String, Object>> executeQuery(ISourceDTO source, SqlQueryDTO queryDTO) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) source;
        Connection connection = null;
        Table table = null;
        ResultScanner rs = null;
        List<Result> results = Lists.newArrayList();
        List<Map<String, Object>> executeResult = Lists.newArrayList();
        try {
            //获取hbase连接
            connection = HbaseConnFactory.getHbaseConn(hbaseSourceDTO, queryDTO);
            //获取hbase扫描列，格式 - 列族:列名
            List<String> columns = queryDTO.getColumns();
            //获取hbase自定义查询的过滤器
            List<com.dtstack.dtcenter.loader.dto.filter.Filter> hbaseFilter = queryDTO.getHbaseFilter();
            String tableNameStr = "";
            if (StringUtils.isNotEmpty(queryDTO.getSchema())) {
                tableNameStr = queryDTO.getSchema() + ":" + queryDTO.getTableName();
            } else {
                tableNameStr = queryDTO.getTableName();
            }
            TableName tableName = TableName.valueOf(tableNameStr);
            table = connection.getTable(tableName);
            List<Filter> filterList = Lists.newArrayList();
            Scan scan = new Scan();
            if (columns != null) {
                for (String column : columns) {
                    String[] familyAndQualifier = column.split(":");
                    if (familyAndQualifier.length < 2) {
                        continue;
                    }
                    scan.addColumn(Bytes.toBytes(familyAndQualifier[0]), Bytes.toBytes(familyAndQualifier[1]));
                }
            }

            boolean isAccurateQuery = false;
            if (hbaseFilter != null && hbaseFilter.size() > 0) {
                for (com.dtstack.dtcenter.loader.dto.filter.Filter filter : hbaseFilter) {
                    if (getAccurateQuery(table, results, filter)) {
                        isAccurateQuery = true;
                        break;
                    }
                    // 针对时间戳过滤器进行封装
                    if ("TimestampFilter".equals(filter.getClass().getSimpleName()) && filter instanceof TimestampFilter) {
                        TimestampFilter timestampFilter = (TimestampFilter) filter;
                        fillTimestampFilter(scan, timestampFilter);
                        continue;
                    }
                    //将core包下的filter转换成hbase包下的filter
                    Filter transFilter = FilterType.get(filter);
                    filterList.add(transFilter);
                }
                FilterList filters = new FilterList(filterList);
                scan.setFilter(filters);
            }
            if (!isAccurateQuery) {
                rs = table.getScanner(scan);
                for (Result r : rs) {
                    results.add(r);
                }
            }

        } catch (Exception e) {
            if (e.getMessage().contains("org.apache.hadoop.hbase.TableNotEnabledException")) {
                throw new DtLoaderException(String.format("表%s,目前处于禁用状态，因此无法进行任何读写操作", queryDTO.getTableName()));
            } else {
                throw new DtLoaderException(String.format("Failed to execute hbase customization,%s", e.getMessage()), e);
            }
        } finally {
            if (hbaseSourceDTO.getPoolConfig() == null || MapUtils.isNotEmpty(hbaseSourceDTO.getKerberosConfig())) {
                close(rs, table, connection);
            } else {
                close(rs, table, null);
            }
        }

        //理解为一行记录
        for (Result result : results) {
            List<Cell> cells = result.listCells();
            long timestamp = 0L;
            HashMap<String, Object> row = Maps.newHashMap();
            for (Cell cell : cells) {
                row.put(ROWKEY, Bytes.toString(cell.getRowArray(), cell.getRowOffset(), cell.getRowLength()));
                String family = Bytes.toString(cell.getFamilyArray(), cell.getFamilyOffset(), cell.getFamilyLength());
                String qualifier = Bytes.toString(cell.getQualifierArray(), cell.getQualifierOffset(), cell.getQualifierLength());
                String value = Bytes.toString(cell.getValueArray(), cell.getValueOffset(), cell.getValueLength());
                row.put(String.format(FAMILY_QUALIFIER, family, qualifier), value);
                //取到最新变动的时间
                if (cell.getTimestamp() > timestamp) {
                    timestamp = cell.getTimestamp();
                }
            }
            row.put(TIMESTAMP, timestamp);
            executeResult.add(row);
        }

        return executeResult;
    }

    /**
     * 填充hbase自定义查询时间戳过滤参数
     *
     * @param scan            scan对象
     * @param timestampFilter 时间戳过滤器
     */
    private void fillTimestampFilter(Scan scan, TimestampFilter timestampFilter) throws IOException {
        CompareOp compareOp = timestampFilter.getCompareOp();
        Long comparator = timestampFilter.getComparator();
        if (Objects.isNull(comparator)) {
            return;
        }
        switch (compareOp) {
            case LESS:
                scan.setTimeRange(TimeRange.INITIAL_MIN_TIMESTAMP, comparator);
                break;
            case EQUAL:
                scan.setTimeStamp(comparator);
                break;
            case GREATER:
                scan.setTimeRange(comparator + 1, TimeRange.INITIAL_MAX_TIMESTAMP);
                break;
            case LESS_OR_EQUAL:
                scan.setTimeRange(TimeRange.INITIAL_MIN_TIMESTAMP, comparator + 1);
                break;
            case GREATER_OR_EQUAL:
                scan.setTimeRange(comparator, TimeRange.INITIAL_MAX_TIMESTAMP);
                break;
            default:
        }
    }

    private boolean getAccurateQuery(Table table, List<Result> results, com.dtstack.dtcenter.loader.dto.filter.Filter filter) throws IOException {
        if (filter instanceof RowFilter) {
            RowFilter rowFilterFilter = (RowFilter) filter;
            if (rowFilterFilter.getCompareOp().equals(CompareOp.EQUAL)) {
                Get get = new Get(rowFilterFilter.getComparator().getValue());
                Result r = table.get(get);
                results.add(r);
                return true;
            }
        }
        return false;
    }

    @Override
    public List<List<Object>> getPreview(ISourceDTO source, SqlQueryDTO queryDTO) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) source;
        Connection connection = null;
        Table table = null;
        ResultScanner rs = null;
        List<Result> results = Lists.newArrayList();
        List<List<Object>> executeResult = Lists.newArrayList();
        try {
            //获取hbase连接
            connection = HbaseConnFactory.getHbaseConn(hbaseSourceDTO, queryDTO);
            String tableNameStr = "";
            if (StringUtils.isNotEmpty(queryDTO.getSchema())) {
                tableNameStr = queryDTO.getSchema() + ":" + queryDTO.getTableName();
            } else {
                tableNameStr = queryDTO.getTableName();
            }
            TableName tableName = TableName.valueOf(tableNameStr);
            table = connection.getTable(tableName);
            Scan scan = new Scan();
            //数据预览限制返回条数
            scan.setMaxResultSize(queryDTO.getPreviewNum());
            scan.setFilter(new PageFilter(queryDTO.getPreviewNum()));
            rs = table.getScanner(scan);
            for (Result r : rs) {
                results.add(r);
            }
        } catch (Exception e) {
            if (e.getMessage().contains("org.apache.hadoop.hbase.TableNotEnabledException")) {
                throw new DtLoaderException(String.format("表%s,目前处于禁用状态，因此无法进行任何读写操作", queryDTO.getTableName()));
            } else {
                throw new DtLoaderException(String.format("Data preview failed,%s", e.getMessage()), e);
            }
        } finally {
            if (hbaseSourceDTO.getPoolConfig() == null || MapUtils.isNotEmpty(hbaseSourceDTO.getKerberosConfig())) {
                close(rs, table, connection);
            } else {
                close(rs, table, null);
            }
        }

        //理解为一行记录
        for (Result result : results) {
            List<Object> data = Lists.newArrayList();
            List<Cell> cells = result.listCells();
            if (CollectionUtils.isEmpty(cells)) {
                continue;
            }
            long timestamp = 0L;
            HashMap<String, Object> row = Maps.newHashMap();
            for (Cell cell : cells) {
                String family = Bytes.toString(cell.getFamilyArray(), cell.getFamilyOffset(), cell.getFamilyLength());
                String qualifier = Bytes.toString(cell.getQualifierArray(), cell.getQualifierOffset(), cell.getQualifierLength());
                String value = Bytes.toString(cell.getValueArray(), cell.getValueOffset(), cell.getValueLength());
//                row.put(String.format(FAMILY_QUALIFIER, family, qualifier), value);
                data.add(value);
                //取到最新变动的时间
                if (cell.getTimestamp() > timestamp) {
                    timestamp = cell.getTimestamp();
                }
            }
//            row.put(ROWKEY, Bytes.toString(cells.get(0).getRowArray(), cells.get(0).getRowOffset(), cells.get(0).getRowLength()));
            data.add(Bytes.toString(cells.get(0).getRowArray(), cells.get(0).getRowOffset(), cells.get(0).getRowLength()));
//            row.put(TIMESTAMP, timestamp);
            data.add(timestamp);
//            executeResult.add(Lists.newArrayList(row));
            executeResult.add(data);
        }
        return executeResult;
    }


    @Override
    public int getPreviewRows(ISourceDTO source, SqlQueryDTO queryDTO) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) source;
        Connection connection = null;
        Table table = null;
        ResultScanner rs = null;
        List<Result> results = Lists.newArrayList();
        List<List<Object>> executeResult = Lists.newArrayList();
        int count = 0;
        try {
            //获取hbase连接
            connection = HbaseConnFactory.getHbaseConn(hbaseSourceDTO, queryDTO);
            String tableNameStr = "";
            if (StringUtils.isNotEmpty(queryDTO.getSchema())) {
                tableNameStr = queryDTO.getSchema() + ":" + queryDTO.getTableName();
            } else {
                tableNameStr = queryDTO.getTableName();
            }
            TableName tableName = TableName.valueOf(tableNameStr);
            table = connection.getTable(tableName);
            Scan scan = new Scan();
            //数据预览限制返回条数
            scan.setMaxResultSize(queryDTO.getPreviewNum());
            scan.setFilter(new PageFilter(queryDTO.getPreviewNum()));
            rs = table.getScanner(scan);
            for (Result r : rs) {
                results.add(r);
            }
            count = results.size();
        } catch (Exception e) {
            if (e.getMessage().contains("org.apache.hadoop.hbase.TableNotEnabledException")) {
                throw new DtLoaderException(String.format("表%s,目前处于禁用状态，因此无法进行任何读写操作", queryDTO.getTableName()));
            } else {
                throw new DtLoaderException(String.format("Data preview failed,%s", e.getMessage()), e);
            }
        } finally {
            if (hbaseSourceDTO.getPoolConfig() == null || MapUtils.isNotEmpty(hbaseSourceDTO.getKerberosConfig())) {
                close(rs, table, connection);
            } else {
                close(rs, table, null);
            }
        }
        return count;
    }

    public static void closeTable(Table table) {
        if (table != null) {
            try {
                table.close();
            } catch (IOException e) {
                throw new DtLoaderException(String.format("hbase can not close table error,%s", e.getMessage()), e);
            }
        }
    }

    private void close(Closeable... closeables) {
        try {
            if (Objects.nonNull(closeables)) {
                for (Closeable closeable : closeables) {
                    if (Objects.nonNull(closeable)) {
                        closeable.close();
                    }
                }
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("hbase can not close table error,%s", e.getMessage()), e);
        }
    }

    @Override
    public List<String> getAllDatabases(ISourceDTO source, SqlQueryDTO queryDTO) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) source;
        Connection connection = null;
        Admin admin = null;
        List<String> namespaces = Lists.newArrayList();
        try {
            //获取hbase连接
            connection = HbaseConnFactory.getHbaseConn(hbaseSourceDTO, queryDTO);
            admin = connection.getAdmin();
            NamespaceDescriptor[] descriptors = admin.listNamespaceDescriptors();
            for (NamespaceDescriptor descriptor : descriptors) {
                namespaces.add(descriptor.getName());
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get namespace list exception：%s", e.getMessage()), e);
        } finally {
            close(admin);
            closeConnection(connection, hbaseSourceDTO);
            destroyProperty();
        }
        return namespaces;
    }

    public static void destroyProperty() {
        System.clearProperty("java.security.auth.login.config");
        System.clearProperty("javax.security.auth.useSubjectCredsOnly");
    }
   /* //获取表命名空间的方法
    @Override
    public List<JSONObject> getAllNameSpaces(ISourceDTO iSource) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        List<JSONObject> namespaceDescriptors = new ArrayList<>();
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO, null);
            admin = hConn.getAdmin();
            NamespaceDescriptor[] listNamespaceDescriptors = admin.listNamespaceDescriptors();
            List<NamespaceDescriptor> namespaceDescriptors1 = Arrays.asList(listNamespaceDescriptors);
            for (NamespaceDescriptor namespaceDescriptor : namespaceDescriptors1) {

                // 获取命名空间的 ACL
                String acl = namespaceDescriptor.getConfigurationValue("hbase.acl");

                // 解析 ACL 并提取权限信息
                if (acl != null && !acl.isEmpty()) {
                    String[] permissions = acl.split(",");
                    for (String permission : permissions) {
                        System.out.println(permission.trim());
                        // 在此处可以进一步解析权限信息，提取所需的权限数据
                    }
                }
                String s = JSONObject.toJSONString(namespaceDescriptor);
                JSONObject jsonObject = JSONObject.parseObject(s);
                namespaceDescriptors.add(jsonObject);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new DtLoaderException(String.format("hbase can not get namespaces error,%s", e.getMessage()), e);
        } finally {
            if (null != admin) {
                try {
                    admin.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }
        return namespaceDescriptors;

    }


    //创建命名空间
    @Override
    public Boolean createNameSpaces(ISourceDTO iSource, List<JSONObject> namespaceDescriptors) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO, null);
            admin = hConn.getAdmin();
            for (JSONObject namespaceDescriptorJson : namespaceDescriptors) {
                NamespaceDescriptor namespaceDescriptor = new ObjectMapper().readValue(namespaceDescriptorJson.toJSONString(), NamespaceDescriptor.class);
                //查询hbase中是否已经存在命名空间
                Boolean databaseExists = this.isDatabaseExists(iSource, namespaceDescriptor.getName());
                //hbase默认有hbase和default的命名空间,不需要创建
                if (databaseExists) {
                    if (!"hbase".equalsIgnoreCase(namespaceDescriptor.getName()) && !"default".equalsIgnoreCase(namespaceDescriptor.getName())) {
                        admin.createNamespace(namespaceDescriptor);
                    }
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;

        } finally {
            if (null != admin) {
                try {
                    admin.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }
        return true;

    }

    //TODO 获取所有表的tabledescription的方法
    @Override
    public List<Object> getAllTableDescriptor(ISourceDTO iSource) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        List<Object> tableDescriptors = new ArrayList<>();
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO, null);
            admin = hConn.getAdmin();
            List<TableDescriptor> tableDescriptorslist = admin.listTableDescriptors();
            for (TableDescriptor tableDescriptor : tableDescriptorslist) {
                tableDescriptors.add(tableDescriptor);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != admin) {
                try {
                    admin.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }
        return tableDescriptors;
    }

    //TODO 添加根据表tableDescriptor创建表的方法
    @Override
    public Boolean createTableDescriptor(ISourceDTO iSource, List<Object> tableDescriptors) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO, null);
            admin = hConn.getAdmin();
            for (Object tableDescriptor : tableDescriptors) {
                //判断表是否存在
                TableDescriptor tableDes = (TableDescriptor) tableDescriptor;
                boolean b = admin.tableExists(TableName.valueOf(tableDes.getTableName().getName()));
                if (!b) {
                    admin.createTable(tableDes);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            if (null != admin) {
                try {
                    admin.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }
        return true;
    }

    //TODO 添加创建快照的方法
    @Override
    public Boolean createTableSnapshot(ISourceDTO iSource, String tableName, String snapshotName) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO, null);
            admin = hConn.getAdmin();
            //根据命名空间获取所有的表
            TableName tabName = TableName.valueOf(tableName);

            //创建表快照
            admin.snapshot(snapshotName, tabName);
            List<SnapshotDescription> snapshotDescriptions = admin.listSnapshots(snapshotName);
            SnapshotDescription snapshotDescription = snapshotDescriptions.get(0);
            //判断快照是否已经创建成功
            boolean snapshotFinished = admin.isSnapshotFinished(snapshotDescription);
            admin.close();
            return snapshotFinished;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != admin) {
                try {
                    admin.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }


        return false;
    }

    //TODO 添加导出快照的方法
    @Override
    public String exportSnapshot(ISourceDTO iSource, String snapshotName, String hbaseExportPath, String resourceJarPath) {
        try {
            Map<String, Object> sourceToMap = HbasePoolManager.sourceToMap(iSource, null);
            Configuration hConfig = HBaseConfiguration.create();
            for (Map.Entry<String, Object> entry : sourceToMap.entrySet()) {
                hConfig.set(entry.getKey(), (String) entry.getValue());
            }
            hConfig.set("mapreduce.app-submission.cross-platform", "true");
            hConfig.set("mapred.jar", resourceJarPath);
            DsgExportSnapshot dsgExportSnapshot = new DsgExportSnapshot();
            hConfig.set("ipc.client.fallback-to-simple-auth-allowed", "true");
            dsgExportSnapshot.setConf(hConfig);
            String[] strings = new String[]{"-snapshot", snapshotName, "-copy-to", hbaseExportPath, "-mappers", "16"};
            dsgExportSnapshot.start(strings);
            return dsgExportSnapshot.getJobID();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }


    //TODO 添加恢复到快照的方法
    @Override
    public Boolean doRestoreHbase(ISourceDTO iSource, String tabName, String snapshotName) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO, null);
            admin = hConn.getAdmin();
            //查询快照是否存在
            List<SnapshotDescription> snapshotDescriptions = admin.listSnapshots(snapshotName);
            SnapshotDescription snapshotDescription = snapshotDescriptions.get(0);
            boolean snapshotFinished = admin.isSnapshotFinished(snapshotDescription);
            if (!snapshotFinished) {
                throw new RuntimeException("当前快照：【" + snapshotName + "】不存在");
            }
            //禁用表
            TableName tableName = TableName.valueOf(tabName);
            //禁用表
            admin.disableTable(tableName);
          *//*  //判断是否禁用成功
            boolean tableDisabled = admin.isTableDisabled(tableName);
            //恢复快照
            if(tableDisabled){*//*
                admin.restoreSnapshot(snapshotName);
//            }
            //启用表
            admin.enableTable(tableName);

            //删除快照
            admin.deleteSnapshot(snapshotName);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }


        return false;
    }
*/
    //TODO 添加删除快照的方法

    /**
     * 判断表是否存在
     */
    public Boolean isTableExistsInDatabase(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        HbaseSourceDTO hbaseSourceDTO = (HbaseSourceDTO) iSource;
        Connection hConn = null;
        Admin admin = null;
        try {
            hConn = HbaseConnFactory.getHbaseConn(hbaseSourceDTO, queryDTO);
            String tableNameStr = "";
            String tableName1 = queryDTO.getTableName();
            if(!tableName1.contains(":")){
                tableNameStr = "default:" + tableName1;
            }else{
                tableNameStr=tableName1;
            }
            TableName tableName = TableName.valueOf(tableNameStr);
            admin = hConn.getAdmin();
            return admin.tableExists(tableName);
        }catch (Exception e){
           log.error("判断表是否存在异常：{}",e.getMessage(),e);
        }finally {
            closeAdmin(admin);
            closeConnection(hConn, hbaseSourceDTO);
        }
        return false;
    }
}