/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.oceanbase;

import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.OceanBaseSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataBaseType;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.sql.Statement;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 14:18 2021/4/21
 */
public class OceanBaseConnFactory extends ConnFactory {
    public OceanBaseConnFactory() {
        driverName = DataBaseType.OceanBase.getDriverClassName();
        this.errorPattern = new OceanBaseErrorPattern();
    }

    @Override
    public Connection getConn(ISourceDTO iSource, String taskParams) throws Exception {
        Connection connection = super.getConn(iSource, taskParams);
        OceanBaseSourceDTO source = (OceanBaseSourceDTO) iSource;
        String schema = source.getSchema();
        if (StringUtils.isNotEmpty(schema)) {
            try (Statement statement = connection.createStatement()) {
                //选择schema
                String useSchema = String.format("USE %s", schema);
                statement.execute(useSchema);
            } catch (Exception e) {
                throw new DtLoaderException(e.getMessage(), e);
            }
        }
        return connection;
    }
}
