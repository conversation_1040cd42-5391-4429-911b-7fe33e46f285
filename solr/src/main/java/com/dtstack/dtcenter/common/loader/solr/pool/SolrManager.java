/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.solr.pool;

import com.dtstack.dtcenter.loader.cache.pool.config.PoolConfig;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.SolrSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.google.common.collect.Maps;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 下午5:14 2021/5/7
 * @Description：
 */
@Slf4j
@NoArgsConstructor
public class SolrManager {
    private volatile static SolrManager manager;

    private volatile Map<String, SolrPool> sourcePool = Maps.newConcurrentMap();

    private static final String SOLR_KEY = "zkHost:%s,chroot:%s";

    public static SolrManager getInstance() {
        if (null == manager) {
            synchronized (SolrManager.class) {
                if (null == manager) {
                    manager = new SolrManager();
                }
            }
        }
        return manager;
    }

    public SolrPool getConnection(ISourceDTO source) {
        String key = getPrimaryKey(source).intern();
        SolrPool SolrPool = sourcePool.get(key);
        if (SolrPool == null) {
            synchronized (SolrManager.class) {
                SolrPool = sourcePool.get(key);
                if (SolrPool == null) {
                    SolrPool = initSource(source);
                    sourcePool.putIfAbsent(key, SolrPool);
                }
            }
        }

        return SolrPool;
    }

    /**
     * 初始化solr pool
     *
     * @param source
     * @return
     */
    public SolrPool initSource(ISourceDTO source) {
        SolrSourceDTO solrSourceDTO = (SolrSourceDTO) source;
        PoolConfig poolConfig = solrSourceDTO.getPoolConfig();
        if (Objects.isNull(poolConfig)) {
            throw new DtLoaderException("init SolrPool fail ,poolConfig can't null");
        }
        SolrPoolConfig config = new SolrPoolConfig();
        config.setMaxWaitMillis(poolConfig.getConnectionTimeout());
        config.setMinIdle(poolConfig.getMinimumIdle());
        config.setMaxIdle(poolConfig.getMaximumPoolSize());
        config.setMaxTotal(poolConfig.getMaximumPoolSize());
        config.setTimeBetweenEvictionRunsMillis(poolConfig.getMaxLifetime() / 10);
        config.setMinEvictableIdleTimeMillis(poolConfig.getMaxLifetime());
        // 闲置实例校验标识，如果校验失败会删除当前实例
        config.setTestWhileIdle(Boolean.TRUE);
        config.setZkHosts(solrSourceDTO.getZkHost());
        config.setChroot(solrSourceDTO.getChroot());

        SolrPool pool = new SolrPool(config);
        pool.addObjects(poolConfig.getMinimumIdle());
        log.info("Get solr data source connection(Pool), zkHost : {}, chroot : {}", solrSourceDTO.getZkHost(), solrSourceDTO.getChroot());
        return pool;
    }

    private String getPrimaryKey(ISourceDTO sourceDTO) {
        SolrSourceDTO solrSourceDTO = (SolrSourceDTO) sourceDTO;
        return String.format(SOLR_KEY, solrSourceDTO.getZkHost(), solrSourceDTO.getChroot());
    }
}
